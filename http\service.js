import { get } from 'lodash';
import request from './request'

let GET = 'GET';
let POST = 'POST';
let PUT = 'PUT';
let DELETE = 'DELETE';

function handleRequest(method, url, param, config) {
  return request({ url, method, param, config })
}

export default {
  nls: {
    // 国际化菜单
    findNlsPorosMenuTree: (param, config) => handleRequest(GET, '/api/luoto-mes/NlsDefApi/findNlsPorosMenuTree', param, config),

    //  获取当前国际化
    getqueryCurrentLocal: (param, config) => handleRequest(GET, '/api/luoto-mes/LocaleController/queryCurrentLocal', param, config),

    //  获取全局国际化
    getGlobalNls: (param, config) => handleRequest(POST, '/api/luoto-mes/MenuItemDefApi/findAllWithEquals', param, config),

    // 同步
    syncAll: (param, config) => handleRequest(POST, '/api/luoto-mes/MenuItemDefApi/syncAll', param, config),

    // locale
    locale: (param, config) => handleRequest(POST, '/api/luoto-mes/locale', param, config),

    // 修改接口
    changeUserLocale: (param, config) => handleRequest(POST, `/api/luoto-mes/UserDefApi/changeUserLocale?locale=${param}`, config),
  },
  /* 公共 */
  common: {
    // 心跳检测 用于token持久化
    heartBeat: (param, config) => handleRequest(GET, '/api/poros-permission/menu/menus?sysCode=luoto-mes-web', param, config),
    // gc获取密钥
    loginKey: (param, config) => handleRequest(GET, `/api/poros-authcenter/secret/${param}`, config),

    //  gc登录
    login: (param, config) => handleRequest(POST, '/api/poros-authcenter/login', param, config),

    // yn登录
    ynLogin: (param, config) => handleRequest(GET, '/api/luoto-mes/gucUser/gucLogin', param, config),

    // 耀能获取用户信息
    ynUserInfo: (param, config) => handleRequest(GET, '/api/guc/current/user-info', param, config),

    // 耀能菜单
    ynMenu: (param, config) => handleRequest(GET, '/api/guc/current/menu/tree', param, config),

    saveOrUpdate: (param, config) => handleRequest(POST, '/api/luoto-mes/gucUser/saveOrUpdate', param, config),

    //  修改密码
    changepassword: (param, config) => handleRequest(PUT, '/api/poros-permission/secStaff/modify/password', param, config),

    // 查询最新版本apk
    getLastApp: (param, config) => handleRequest(GET, '/api/luoto-mes/swpSysPdaVersion/getFastVersion', param, config),

    // 权限菜单
    mianmenu: (param, config) => handleRequest(GET, '/api/poros-permission/menu/menus?sysCode=luoto-mes-web', param, config),


    //用户信息
    usermessage: (param, config) => handleRequest(GET, '/api/poros-authcenter/user/message', param, config),

    // 消息发送-通用业务执行接口
    commonExec: (param, config) => handleRequest(POST, '/api/luoto-mes/common/exec', param, config),
    // 打印
    queryPrintLotInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/LotMgtController/queryPrintLotInfo', param, config),

    // 分页查询批次补打信息
    queryPagePrintLotInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/LotMgtController/queryPagePrintLotInfo', param, config),

    // 匀浆/涂布/辊压/分切/产出获取设备产出数量 匀浆operateNo传10，涂布operateNo传20，辊压operateNo传30，分切operateNo传40
    queryMachineOutput: (param, config) => handleRequest(POST, '/api/luoto-mes/equPortFeedQty/queryMachineOutput', param, config),

    // 单位转换
    findAllWithEquals: (param, config) => handleRequest(POST, '/api/luoto-mes/unitConversionApi/findAllWithEquals', param, config),

    //枚举
    getEnumValue: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_C_GetEnumValue", param, config),

    getDictByQueryId: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=${param}`, config),

    //  打印
    print: (param, config) => handleRequest(POST, `/api/getech-mes/PrintAssignApi/print`, param, config),
  },
  Dicts: {
    getstockLocationCodeList: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=ListStockLocationCode`, param, config),
  },

  /* 材料上料 */
  MaterialLoading: {
    // 材料上料-设备信息
    getMachineNameByPortName: (param, config) => handleRequest(GET, '/api/luoto-mes/PortApi/getMachineNameByPortName', param, config),
    // 材料上料-设备信息
    queryPdaMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/getMachineData', param, config),

    // 材料上料-产品编码
    // getProductSpecList: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/getProductSpecList', param, config),
    getProductSpecList: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/listReleasedProductOrder', param, config),

    // 材料上料-工单信息
    getProductOrderList: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/getProductOrderList', param, config),

    // 材料上料-标签条码校验
    getConsumableDataCheck: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/getConsumableDataCheck', param, config),

    // 材料上料-标签条码信息
    getConsumableData: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/getConsumableData', param, config),

    // 材料上料-已上物料明细
    getConsumableLoadingData: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/getConsumableLoadingData', param, config),

    // 材料上料-BOM信息
    getBomData: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableLoadingController/getBomData', param, config),

    // 材料上料
    ConsumableLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/ConsumableLoadingController/ConsumableLoading', param, config),

    // 材料卸料-设备信息
    UnLoadingQueryPdaMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableUnLoadingController/getMachineData', param, config),

    // 材料上料-已上物料明细
    getConsumableLoadingData: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableUnLoadingController/getConsumableLoadingData', param, config),

    // 材料卸料
    ConsumableUnLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/ConsumableUnLoadingController/ConsumableUnLoading', param, config),

    // 物料接收确认-根据发料单号查询领料单信息
    getProductOrderPickList: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=ProductOrderPick_C_GetProductOrderPickList", param, config),

    // 物料接收确认-根据领料单号查询获取物料
    getProductOrderMaterialList: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=ProductOrderPick_C_GetProductOrderMaterialList", param, config),

    // 物料接收确认-接收和拒收
    ProductOrderPickUpdate: (param, config) => handleRequest(POST, "/api/luoto-mes/ProductOrderPickApi/update", param, config),
  },

  /* 设置生产型号 */
  SetProductionModel: {
    // 设备扫描
    queryPdaMachineName: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_C_GetMachineInfo", param, config),

    // 设置生产参数
    MachineTaskApiMerge: (param, config) => handleRequest(POST, "/api/luoto-mes/MachineTaskApi/merge", param, config),

    // 产品列表
    GetProductSpecInfo: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_C_GetProductSpecInfo", param, config),

    // 查看已设置数据
    GetMachineTaskList: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_C_GetMachineTaskList", param, config),

    // 查看工艺卡参数
    GetMachineInputParamInfo: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_C_GetMachineInputParamInfo", param, config),


    // 获取设备换型工单
    listProductOrder: (param, config) => handleRequest(POST, "/api/luoto-mes/MachineTaskController/listProductOrder", param, config),

    // 获取设备换型工单
    machineTaskControllerAdd: (param, config) => handleRequest(POST, "/api/luoto-mes/MachineTaskController/add", param, config),

    // 工段
    GetWorkShop: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=MachineTask_GetWorkShop", param, config),


  },
  /* 工单切换 */
  WorkorderChange: {
    // listDeleteLot
    listDeleteLot: (param, config) => handleRequest(POST, "/api/luoto-mes/MachineTaskController/listDeleteLot", param, config),

    deleteLot: (param, config) => handleRequest(GET, `/api/luoto-mes/MachineTaskController/deleteLot?lotName=${param}`, config),

    undoDeleteLot: (param, config) => handleRequest(POST, `/api/luoto-mes/MachineTaskController/undoDeleteLot?lotName=${param}`, config),
  },

  /* 搅拌工序-开始 */
  materialFeeding: {
    //PDA根据设备号查询设备信息
    queryPdaMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getMixingMachineData', param, config),

    //PDA根据设储蓄编码查询信息
    queryDurableName: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getStorageTankData', param, config),

    // 获取产品列表
    getProductSpecByMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getProductSpecByMachineName', param, config),

    //根据工序 设备号 产品编码 查询工艺卡信息
    validateIsExistOperationCard: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/validateIsExistOperationCard', param, config),

    // 搅拌开始
    startMixingMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/startMixingMsgProcessor', param, config),
  },
  // 搅拌-投料
  materialunFeeding: {
    // 根据设备查询投料信息
    queryPdaMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getMixingMachineDataForFeed', param, config),

    // 投料-校验物料是否存在
    validateConsumable: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/validateConsumable', param, config),

    // 投料-校验物料
    validateFIFO: (param, config) => handleRequest(GET, '/api/luoto-mes/ConsumableController/validateFIFO', param, config),

    // 搅拌开始-继续投料
    mixingFeedMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/mixingFeedMsgProcessor', param, config),

    // 搅拌开始-投料完成
    finishFeedMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/finishFeedMsgProcessor', param, config),

    // 可用物料查询
    getConsumableList: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getConsumableList', param, config),

    // 已投物料查询
    getFeedConsumableList: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getFeedConsumableList', param, config),
  },
  // 搅拌-搅拌
  stir: {
    // 根据设备查询投料信息
    queryPdaMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getMixingMachineDataForMixingStart', param, config),

    // 搅拌搅拌-开始
    mixingStart: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/mixingStart', param, config),

    // 搅拌搅拌-结束
    mixingEnd: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/mixingEnd', param, config),
  },
  // 搅拌-产出
  stirProduce: {
    // 根据设备查询投料信息
    queryPdaMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getMixingMachineDataForEndMixing', param, config),

    // 搅拌产出确认
    endMixing: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/endMixing', param, config),

  },
  // 搅拌-异常处理
  stirExceptionHandling: {
    // 搅拌-异常处理-获取条码信息
    getLotInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getLotInfo', param, config),

    // 物料信息
    getConsumableInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/MixingTrackingController/getConsumableInfo', param, config),

    // 投料
    feedingMaterialForNg: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/feedingMaterialForNg', param, config),

    // 获取之前的搅拌
    mixingForNg: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/mixingForNg', param, config),

    // 搅拌开始
    mixingStartForNg: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/mixingStartForNg', param, config),

    // 搅拌结束
    mixingEndForNg: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/mixingEndForNg', param, config),

    // 产出确认
    endMixing: (param, config) => handleRequest(POST, '/api/luoto-mes/MixingTrackingController/endMixing', param, config),
  },
  // 调整工序
  ProcessAdjustment: {
    // 在制品条码信息
    getLotData: (param, config) => handleRequest(GET, '/api/luoto-mes/LotProcessOperationChangeController/getLotData', param, config),
    // 通过载具获取在制品条码信息
    getLotDatas: (param, config) => handleRequest(GET, '/api/luoto-mes/LotProcessOperationChangeController/getLotDatas', param, config),

    // 调整工序
    getProcessOpData: (param, config) => handleRequest(GET, '/api/luoto-mes/LotProcessOperationChangeController/getProcessOpData', param, config),

    // 调整工序-确认
    changeOperationByLotName: (param, config) => handleRequest(POST, '/api/luoto-mes/LotProcessOperationChangeController/changeOperationByLotName', param, config),
  },
  /* 涂布浆料批次指定 */
  Size: {
    // 涂布机扫描
    queryPdaMachineName: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetProductOrderByMachineName", param, config),

    // 涂布机开始和解绑扫描
    getDeassignInfoByMachineName: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetDeassignInfoByMachineName", param, config),

    // 储存机扫描
    queryPdaSaveNo: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetConsumableByMachineName", param, config),

    // 储存机扫描
    getCoatingQuantity: (param, config) => handleRequest(POST, "/api/luoto-mes/CoatingTrackingController/getCoatingQuantity", param, config),

    //浆料批次查询
    getConsumableByMachineName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_CarrierListByConsumableSpecName', param, config),

    // 批次绑定
    assignCoating: (param, config) => handleRequest(POST, "/api/luoto-mes/CoatingTrackingController/assignCoating", param, config),

    // 批次解绑
    deAssignCoating: (param, config) => handleRequest(POST, "/api/luoto-mes/CoatingTrackingController/deAssignCoating", param, config),

    // 校验
    verificationCarrierName: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_G_MachineListByCarrierName", param, config),

    // 涂布工序- 单面开始页面  工艺卡查询
    GetOperationCard: (param, config) => handleRequest(GET, "/api/luoto-mes/CoatingTrackingController/singleCoatingGetOperationCard", param, config),

    // 涂布工序- 单面开始页面  浆料物料信息查询 
    GetFeedingmaterial: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetFeedingmaterial", param, config),

    // 涂布工序- 单面开始页面  浆料物料信息查询 
    GetFeedingmaterialList: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetFeedingmaterialList", param, config),

    startSingleCoating: (param, config) => handleRequest(POST, "/api/luoto-mes/CoatingTrackingController/startSingleCoating", param, config),

    // 涂布指定/涂布单面开始的参数检验
    singleCoatingCheck: (param, config) => handleRequest(GET, '/api/luoto-mes/CoatingTrackingController/singleCoatingCheck', param, config),

    // 涂布指定-FOFI参数检验
    singleCoatingFIFOCheck: (param, config) => handleRequest(POST, "/api/luoto-mes/CoatingTrackingController/singleCoatingFIFOCheck", param, config),

    // 记录不良
    recordNgInfo: (param, config) => handleRequest(POST, "/api/luoto-mes/SinglerRollTrackingController/recordNgInfo", param, config),

  },
  /* 极卷烘烤 */
  bake: {
    // 烘烤设备扫描 
    queryPdaMachineName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_OvenByMachineName', param, config),

    // 烘洞号扫描
    queryPdaByPortName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_OvenByPortName', param, config),

    // 极卷扫描 
    queryPdaByLotName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_OvenByLotName', param, config),

    // 极卷条数
    queryPdaByLotList: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_OvenGetLotList', param, config),

    // 开始烘烤
    startOven: (param, config) => handleRequest(POST, '/api/luoto-mes/OvenTrackingController/startOven', param, config),

    // 烘烤结束
    endOven: (param, config) => handleRequest(POST, '/api/luoto-mes/OvenTrackingController/endOven', param, config),

  },

  /* 电芯烘烤 */
  bakeCell: {
    // 烘烤设备扫描 
    getMachineInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/CellOvenTrackingController/getMachineInfo', param, config),

    // 烘洞号扫描
    checkPort: (param, config) => handleRequest(GET, '/api/luoto-mes/CellOvenTrackingController/checkPort', param, config),

    // 载具扫描 
    getDurableInfo: (param, config) => handleRequest(POST, '/api/luoto-mes/CellOvenTrackingController/getDurableInfo', param, config),

    // 已扫码载具
    getAssignCarrierInfo: (param, config) => handleRequest(POST, '/api/luoto-mes/CellOvenTrackingController/getAssignCarrierInfo', param, config),

    // 开始烘烤
    startOven: (param, config) => handleRequest(POST, '/api/luoto-mes/CellOvenTrackingController/startCellOven', param, config),

    // 烘烤结束
    endOven: (param, config) => handleRequest(POST, '/api/luoto-mes/CellOvenTrackingController/endCellOven', param, config),

  },

  // 极卷
  Polar: {
    // 上卷提交校验
    PoleRollLoadingCheck: (param, config) => handleRequest(POST, '/api/luoto-mes/PoleRollLoadingController/PoleRollLoadingCheck', param, config),

    // 卸卷查询校验
    getMachineDataCheck: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollUnLoadingController/getMachineDataCheck', param, config),

    // 极卷上卷-设备信息
    getMachineData: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollLoadingController/getMachineData', param, config),

    // 极卷合卷-设备信息
    getMachineDataForJoinRoll: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollLoadingController/getMachineDataForJoinRoll', param, config),

    // 极卷上卷-上卷和合卷上卷-上卷
    PoleRollLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/PoleRollLoadingController/PoleRollLoading', param, config),

    // 极卷上卷-可上极卷查询
    getPoleRollAvailableData: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollLoadingController/getPoleRollAvailableDataByMachine', param, config),

    // 合卷上卷-可上极卷查询
    getPoleRollAvailableDataByMachineNameAndWorkOrderName: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollLoadingController/getPoleRollAvailableDataByMachineNameAndWorkOrderName', param, config),

    // 极卷上卷-极卷条码信息
    getPoleRollData: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollLoadingController/getPoleRollData', param, config),

    // 合卷上卷-极卷条码信息
    getPoleRollDataForRoll: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollLoadingController/getPoleRollDataForRoll', param, config),

    // 极卷卸卷-设备信息
    queryPdaMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/PoleRollUnLoadingController/getMachineData', param, config),

    // 极卷卸卷-卸卷
    PoleRollUnLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/PoleRollUnLoadingController/PoleRollUnLoading', param, config),

    // 单卷开始 - 设备扫码
    getMachineDataForStart: (param, config) => handleRequest(POST, '/api/luoto-mes/SinglerRollTrackingController/getMachineDataForStart', param, config),

    //单卷开始 - 开始
    singlerRollStartMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/SinglerRollTrackingController/singlerRollStartMsgProcessor', param, config),

    // 完工-根据设备获取信息
    getMachineDataForFinish: (param, config) => handleRequest(POST, '/api/luoto-mes/SinglerRollTrackingController/getMachineDataForFinish', param, config),

    // 完工-完工
    singlerRollFinishMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/SinglerRollTrackingController/singlerRollFinishMsgProcessor', param, config),

    // 完工-完工
    singlerRollFinishMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/SinglerRollTrackingController/singlerRollFinishMsgProcessor', param, config),
  },

  // 模切
  DieCutting: {
    // 模切上卷-根据设备获取信息
    getStartDataByMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/DieCuttingTrackingController/getStartDataByMachineName', param, config),

    // 模切-获取极卷信息
    getConsumableData: (param, config) => handleRequest(GET, '/api/luoto-mes/DieCuttingTrackingController/getConsumableData', param, config),

    // 模切-上卷
    DieCuttingUpRollingMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/DieCuttingTrackingController/DieCuttingUpRollingMsgProcessor', param, config),

    // 模切开始 - 设备扫描查询 
    queryPdaMachineName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_DieCuttingByMachineName', param, config),

    // 模切开始  - 弹夹扫描查询
    CheckAndGetDurableByDurableName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_CheckAndGetDurableByDurableName', param, config),

    // 模切开始  - 开始
    DieCuttingStartMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/DieCuttingTrackingController/DieCuttingStartMsgProcessor', param, config),

    // 模切下料-根据设备获取信息
    DieCuttingByMachineName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_DieCuttingByMachineName', param, config),

    // 模切下料-保存
    DieCuttingConsumeMaterialMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/DieCuttingTrackingController/DieCuttingConsumeMaterialMsgProcessor', param, config),

    // 模切下料-保存检验
    checkDieCuttingConsumeMaterialMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/DieCuttingTrackingController/checkDieCuttingConsumeMaterialMsgProcessor', param, config),

    // 模切下料-记录不良-保存
    DieCuttingSaveNgLotRecord: (param, config) => handleRequest(POST, '/api/luoto-mes/DieCuttingTrackingController/DieCuttingSaveNgLotRecord', param, config),

    // 模切下料-记录不良-查询列表
    DieCuttingNgLotRecordList: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_DieCuttingNgLotRecordList', param, config),

    // 模切下料-记录不良-删除
    DieCuttingDeleteNgLotRecord: (param, config) => handleRequest(POST, '/api/luoto-mes/DieCuttingTrackingController/DieCuttingDeleteNgLotRecord', param, config),

    // 模切下料-极卷信息查询
    DieCuttingPoleRoll: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_DieCuttingPoleRoll', param, config),

    // 模切下料-单位转换
    listUnitConversion: (param, config) => handleRequest(POST, '/api/luoto-mes/unitConversionApi/listUnitConversion', param, config),

    // 模切卸卷-根据设备获取信息
    getFinishDataByMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/DieCuttingTrackingController/getFinishDataByMachineName', param, config),

    // 模切卸卷-卸卷
    DieCuttingUnMountRollingMsgProcessor: (param, config) => handleRequest(POST, '/api/luoto-mes/DieCuttingTrackingController/DieCuttingUnMountRollingMsgProcessor', param, config),

    // 模切卸卷-查询不良代码类型
    GetReasonCodeType: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=GetReasonCodeType', param, config),

    // 模切卸卷-查询不良代码
    GetReasonCode: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=GetReasonCode', param, config),

    // 模切卸卷-查询不良代码
    GetReasonCodeByProcessOperationName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=getReasonCodeByProcessOperationName', param, config),

    // 模切卸卷-保存不良代码
    insert: (param, config) => handleRequest(POST, '/api/luoto-mes/NgLotRecordTempApi/insert', param, config),

    // 模切卸卷-不良代码删除
    delete: (param, config) => handleRequest(POST, '/api/luoto-mes/NgLotRecordTempApi/delete', param, config),

    // 模切卸卷-查询不良列表
    findAllWithEquals: (param, config) => handleRequest(POST, '/api/luoto-mes/NgLotRecordTempApi/findAllWithEquals', param, config),

    // 模切-读取数量
    getMachineGoodQuality: (param, config) => handleRequest(GET, '/api/luoto-mes//DieCuttingTrackingController/getMachineGoodQuality', param, config),

    // 模切-读取数量-新增
    readMachineGoodQuality: (param, config) => handleRequest(GET, '/api/luoto-mes/DieCuttingTrackingController/getData', param, config),
  },

  /* 产品载具载具绑定与解绑 */
  carrierIsBind: {
    // 根据载具编码查询已绑定产品
    GetProductAndDurableByDurableName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetProductAndDurableByDurableName', param, config),


    // 扫描在制品条码
    GetProductAndDurableByLotName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetProductAndDurableByLotName', param, config),

    // 扫描在制品条码
    ContainerBindOrUnBind: (param, config) => handleRequest(POST, '/api/luoto-mes/IotController/ContainerBindOrUnBind', param, config),

    //验证质检状态
    lotGrade: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=lot_lotGrade', param, config),


    // 载具与产品操作(绑定)
    assignStack: (param, config) => handleRequest(POST, '/api/luoto-mes/CarrierController/assignStack', param, config),

    // 载具与产品操作(解绑)
    deAssignStack: (param, config) => handleRequest(POST, '/api/luoto-mes/CarrierController/deAssignStack', param, config),

    // 扫描在制品条码
    GetProductListByDurableName: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetProductListByDurableName', param, config),
  },

  // 报工入库
  submitWork: {
    // 获取报工工单详情
    detail: (param, config) => handleRequest(POST, '/api/luoto-mes/productRequest/erp/detail', param, config),

    //获取报工工单列表
    listProductRequest: (param, config) => handleRequest(POST, '/api/luoto-mes/productRequest/erp/listProductRequest', param, config),

    //报工状态下拉接口
    listEnum: (param, config) => handleRequest(POST, '/api/luoto-mes/enumDefValue/listEnum', param, config),

    //报工入库
    erpOwlProductRequest: (param, config) => handleRequest(POST, '/api/luoto-mes/rabbit/publish/erpOwlProductRequest', param, config),

    //根据工单获取批次条码列表(模组或包装段)
    listProductRequestLot: (param, config) => handleRequest(POST, '/api/luoto-mes/lot/modulePack/listProductRequestLot', param, config),
  },

  // 冲壳上料/ 卸料
  punchingShell: {
    // 批次号
    findAllWithEquals: (param, config) => handleRequest(POST, '/api/luoto-mes/ConsumableApi/findAllWithEquals', param, config),

    // 校验是否存在已上铝塑膜
    validateMount: (param, config) => handleRequest(GET, '/api/luoto-mes/PouchFormingTrackingController/validateMount', param, config),

    // 上料
    monut: (param, config) => handleRequest(GET, '/api/luoto-mes/PouchFormingTrackingController/monut', param, config),

    // 校验是否未上铝塑膜
    validateUnmount: (param, config) => handleRequest(GET, '/api/luoto-mes/PouchFormingTrackingController/validateUnmount', param, config),

    // 卸料
    unmount: (param, config) => handleRequest(GET, '/api/luoto-mes/PouchFormingTrackingController/unmount', param, config),
  },
  // 半成品载具上料/ 卸料
  SemiFinishedVehicle: {
    // 半成品载具上料--设备查询
    getMachineData: (param, config) => handleRequest(GET, '/api/luoto-mes/HALTrackingController/getMachineData', param, config),

    // 半成品载具上料-产品编码
    getProductSpecList: (param, config) => handleRequest(GET, '/api/luoto-mes/HALTrackingController/getProductSpecList', param, config),

    // 半成品载具上料-工单信息
    getProductOrderList: (param, config) => handleRequest(GET, '/api/luoto-mes/HALTrackingController/getProductOrderList', param, config),

    // 半成品载具上料-载具查询信息
    getDurableNameData: (param, config) => handleRequest(GET, '/api/luoto-mes/HALTrackingController/getDurableNameData', param, config),

    // 半成品载具上料-BOM信息
    getBomData: (param, config) => handleRequest(GET, '/api/luoto-mes/HALTrackingController/getBomData', param, config),

    // 半成品载具上料-上料
    HALLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/HALTrackingController/HALLoading', param, config),

    // 半成品载具上料-卸料
    HALUnLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/HALTrackingController/HALUnLoading', param, config),

    // 半成品载具上料-已上物料明细
    getConsumableLoadingData: (param, config) => handleRequest(GET, '/api/luoto-mes/HALTrackingController/getConsumableLoadingData', param, config),
  },
  // 质量管理
  QualityControl: {
    // 不良提交和判定
    // 在制品条码信息
    getLotListData: (param, config) => handleRequest(GET, '/api/luoto-mes/NGLotRecordController/getLotListData', param, config),
    // 不良提交
    NgLotRecordAndJudgeSubmit: (param, config) => handleRequest(POST, '/api/luoto-mes/NGLotRecordController/NgLotRecordAndJudgeSubmit', param, config),
    // 不良现象信息
    getReasonCodeList: (param, config) => handleRequest(GET, '/api/luoto-mes/NGLotRecordController/getReasonCodeList', param, config),
    // 材料标签条码信息
    getConsumableData: (param, config) => handleRequest(GET, '/api/luoto-mes/NGConsumableRecordController/getConsumableData', param, config),
    // 设备信息
    getMachineList: (param, config) => handleRequest(GET, '/api/luoto-mes/NGConsumableRecordController/getMachineList', param, config),
    // 材料不良现象信息
    getClReasonCodeList: (param, config) => handleRequest(GET, '/api/luoto-mes/NGConsumableRecordController/getReasonCodeList', param, config),
    // 材料提交
    NgConsumableRecordAndJudgeSubmit: (param, config) => handleRequest(POST, '/api/luoto-mes/NGConsumableRecordController/NgConsumableRecordAndJudgeSubmit', param, config),
    // 在制品不良记录和判定-弹夹信息
    getDurableInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/NGLotRecordController/getDurableInfo', param, config),

    // 首检-巡检
    // 首检任务维护界面 -根据设备获取信息
    getMachineDataForFirstInspection: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/getMachineDataForFirstInspection', param, config),
    // 首检任务维护界面 -现存首检任务
    getInspectionTaskList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/getInspectionTaskList', param, config),
    // 首检任务维护界面 -删除任务
    inspectionTaskDelete: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/inspectionTask/delete', param, config),
    // 首检任务维护界面 -创建任务
    startInspectionTask: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/startInspectionTask', param, config),
    // 首检取样界面 -样品条码查询
    getFirstLotData: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/getLotData', param, config),
    // 首检取样界面 -已取样信息
    getFinshSampleList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/getFinshSampleList', param, config),
    // 首检取样界面 -取样完成
    finshSample: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/finshSample', param, config),
    // 首检取样界面 -枚举
    getEnumValue: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=Common_C_GetEnumValue", param, config),
    // 首检参数录入界面 -首检任务编码
    getTaskNoList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/paramInput/getTaskNoList', param, config),
    // 首检参数录入界面 -样品条码
    getLotNameList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/paramInput/getLotNameList', param, config),
    // 首检参数录入界面 -参数录入明细
    getInspectiontemplatedetails: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/paramInput/getInspectiontemplatedetails', param, config),
    // 首检参数录入界面 -保存
    paramInputSave: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/paramInput/save', param, config),
    // 首检整体判定界面 -首检任务编码查询
    waitJudgeGetTaskNoList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/waitJudge/getTaskNoList', param, config),
    // 首检整体判定界面 -样品检验结果
    getInspectionTaskDetailList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/waitJudge/getInspectionTaskDetailList', param, config),
    // 物料接收确认-根据发料单号查询领料单信息
    waitJudgeGetReasonCodeList: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=GetInspectionReasonCode", param, config),
    // 首检整体判定界面 -保存
    waitJudgeSave: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/waitJudge/save', param, config),
  },
  // 工装
  ToolingAndInspection: {
    // 工装清洗、工装校准扫码
    getDurableDataFirst: (param, config) => handleRequest(POST, '/api/luoto-mes/DurableApi/findAllWithEquals', param, config),
    // 工装设备
    DurableSpecApi: (param, config) => handleRequest(POST, '/api/luoto-mes/DurableSpecApi/findAllWithEquals', param, config),
    // 工装清洗
    HALUnLoading: (param, config) => handleRequest(GET, '/api/luoto-mes/DurableController/cleanDurable', param, config),
    // 工装校准
    finishCalibration: (param, config) => handleRequest(GET, '/api/luoto-mes/DurableController/finishCalibration', param, config),
    // 工装安装
    install: (param, config) => handleRequest(POST, '/api/luoto-mes/ToolController/durableLoading', param, config),
    // 工装安装编码查询
    getDurableData: (param, config) => handleRequest(GET, '/api/luoto-mes/ToolController/getDurableData', param, config),
    // 工装安装设备编码查询
    getMachineData: (param, config) => handleRequest(GET, '/api/luoto-mes/ToolController/getMachineData', param, config),
    // 工装安装已安装明细
    getDurableListByMachineName: (param, config) => handleRequest(GET, '/api/luoto-mes/ToolController/getDurableListByMachineName', param, config),
    // 工装安装设备位置
    getMachinePort: (param, config) => handleRequest(GET, '/api/luoto-mes/ToolController/getMachinePort', param, config),
    // 工装安装-检查装已经达到预警（警戒）使用寿命
    checkUsedCount: (param, config) => handleRequest(POST, '/api/luoto-mes/ToolController/checkUsedCount', param, config),
    // 工装安装-卸载
    durableUnLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/ToolController/durableUnLoading', param, config),
    // 工装清洗履历
    findAllHistWithEqualsOnPage: (param, query, config) => handleRequest(POST, `/api/luoto-mes/DurableApi/findAllHistWithEqualsOnPage?page=${query.page}&&size=${query.size}`, param, config),
  },
  // 设备管理
  EquipmentManagement: {
    // 设备清洗扫码
    getMachineCleanInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/EQPController/getMachineInfo', param, config),
    // 设备校准扫码
    getMachineCheckInfo: (param, config) => handleRequest(POST, '/api/luoto-mes/MachineApi/findAllWithEquals', param, config),
    // 完成清洗
    HALUnLoading: (param, config) => handleRequest(POST, '/api/luoto-mes/EQPController/finishClean', param, config),
    // 完成校准
    finishCalibration: (param, config) => handleRequest(GET, '/api/luoto-mes/EQPController/finishCalibration', param, config),
    // 设备清洗履历
    findAllHistWithEqualsOnPage: (param, query, config) => handleRequest(POST, `/api/luoto-mes/MachineApi/findAllHistWithEqualsOnPage?page=${query.page}&&size=${query.size}`, param, config),
    // 设备校准履历
    // findAllHistWithEqualsOnPage: (param, query, config) => handleRequest(POST, `/api/luoto-mes/MachineApi/findAllHistWithEqualsOnPage?page=${query.page}&&size=${query.size}`, param, config),
  },
  // 调机
  Tuning: {
    // 设备扫码获取参数
    getInfoByMahcine: (param, config) => handleRequest(GET, '/api/luoto-mes/DebugMahcineController/getInfoByMahcine', param, config),
    // 调机提交
    saveResult: (param, config) => handleRequest(POST, '/api/luoto-mes/DebugMahcineController/saveResult', param, config),
    // 调机记录
    getHist: (param, config) => handleRequest(GET, '/api/luoto-mes/DebugMahcineController/getHist', param, config),
  },
  // 静置
  stand: {
    // 库位编码查询
    getPortInfo: (param, config) => handleRequest(GET, '/api/luoto-mes/StandTrackingController/getPortInfo', param, config),
    // 扫描载具条码
    getDurableInfo: (param, config) => handleRequest(POST, '/api/luoto-mes/StandTrackingController/getDurableInfo', param, config),
    // 静置开始
    startStand: (param, config) => handleRequest(POST, '/api/luoto-mes/StandTrackingController/startStand', param, config),
    // 静置结束
    endStand: (param, config) => handleRequest(POST, '/api/luoto-mes/StandTrackingController/endStand', param, config),
  },



  /* 邢东*/
  //  物料管理-扫码入库
  WarehouseOrdersController: {
    getQueryByViewId: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/queryByViewId?viewId=WarehouseOrderList`, param, config),

    // PDA分页查询入库单列表
    queryPageList: (param, config) => handleRequest(POST, `/api/luoto-mes/WarehouseOrdersController/queryPageList`, param, config),

    // 查询入库单详情
    queryDetail: (param, config) => handleRequest(POST, `/api/luoto-mes/WarehouseOrdersController/queryDetail`, param, config),

    // 扫码
    scan: (param, config) => handleRequest(POST, `/api/luoto-mes/WarehouseOrdersController/scan`, param, config),

    // 撤销
    cancel: (param, config) => handleRequest(POST, `/api/luoto-mes/WarehouseOrdersController/cancel`, param, config),

    // 入库
    pdaWare: (param, config) => handleRequest(POST, `/api/luoto-mes/WarehouseOrdersController/pdaWare`, param, config),

  },

  //  物料管理-扫码出库
  DeliveryOrderController: {
    // 分页查询出库单列表
    queryPageList: (param, config) => handleRequest(POST, `/api/luoto-mes/DeliveryOrderController/queryPageList`, param, config),

    // 查询入库单详情
    queryDetail: (param, config) => handleRequest(POST, `/api/luoto-mes/DeliveryOrderItemController/queryList`, param, config),

    // 扫码
    scan: (param, config) => handleRequest(POST, `/api/luoto-mes/DeliveryOrderItemController/scanConsumable`, param, config),

    // 撤销
    cancel: (param, config) => handleRequest(POST, `/api/luoto-mes/DeliveryOrderItemController/revokeScanConsumable`, param, config),

    // 出库
    deliveryPda: (param, config) => handleRequest(POST, `/api/luoto-mes/DeliveryOrderController/deliveryPda`, param, config),

  },

  // 电芯绑盘_拆盘
  BindingUnpacking: {
    // 根据载具类型 + 载具编码获取载具信息
    getDurableInfo: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=BindOrUnBind_GetDurableInfo", param, config),

    // 扫描条码
    bindOrUnBind: (param, config) => handleRequest(POST, `/api/luoto-mes/DurableController/bindOrUnBind`, param, config),

    // 获取载具已装条码列表
    BindOrUnBindGetLotList: (param, config) => handleRequest(POST, "/api/luoto-mes/QueryDefApi/query?queryId=BindOrUnBind_GetLotList", param, config),
  },


  UnqualifiedEntry: {
    // 扫描条码
    scanLotName: (param, config) => handleRequest(POST, `/api/luoto-mes/DisposalOrderController/scanLotName`, param, config),

    add: (param, config) => handleRequest(POST, `/api/luoto-mes/DisposalOrderController/add`, param, config),
  },


  TrayWarehousing: {
    // 托盘物料信息查询
    trayMaterialInfo: (param, config) => handleRequest(POST, `/api/luoto-mes/WmsController/trayMaterialInfo`, param, config),

    // 物料接收-扫描物料箱标签
    scanBoxNo: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayWarehousingController/scanBoxNo`, param, config),

    // 物料接收-提交
    submit: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayWarehousingController/submit`, param, config),
  },


  TrayWarehousingItemController: {
    // 物料拆包-扫描物料箱标签
    scanBoxNo: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayWarehousingItemController/scanBoxNo`, param, config),

    // 物料拆包-扫描最小包装条码
    scanConsumable: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayWarehousingItemController/scanConsumable`, param, config),

    // 物料拆包-提交
    submit: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayWarehousingItemController/submit`, param, config),
  },


  standingStart: {
    // 1、静置开始扫描条码：
    GetStartStewingInfo: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=Consumable_GetStartStewingInfo`, param, config),

    //2、静置结束扫描条码：
    GetEndStewingInfo: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=Consumable_GetEndStewingInfo`, param, config),

    // 3、静置开始
    startStewing: (param, config) => handleRequest(POST, `/api/luoto-mes/ConsumableController/startStewing`, param, config),

    // 4、静置结束
    endStewing: (param, config) => handleRequest(POST, `/api/luoto-mes/ConsumableController/endStewing`, param, config),
  },


  TrayReturnController: {
    // 扫描托盘标签
    scanTrayNo: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayReturnController/scanTrayNo`, param, config),
    // 扫描物料标签
    scanConsumable: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayReturnController/scanConsumable`, param, config),
    // 提交
    submit: (param, config) => handleRequest(POST, `/api/luoto-mes/TrayReturnController/submit`, param, config),
  },

  ReturnOrderController: {
    // 扫描托盘标签
    scanTrayNo: (param, config) => handleRequest(POST, `/api/luoto-mes/ReturnOrderController/scanTrayNo`, param, config),
    // 提交
    submit: (param, config) => handleRequest(POST, `/api/luoto-mes/ReturnOrderController/submit`, param, config),

    //确认或驳回获取退库单列表
    ReturnOrder_ListReturnOrder: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=ReturnOrder_ListReturnOrder`, param, config),
    //根据退库单获取数据列表
    listReturnOrder: (param, config) => handleRequest(POST, `/api/luoto-mes/ReturnOrderController/listReturnOrder`, param, config),
    // 确认
    finish: (param, config) => handleRequest(POST, `/api/luoto-mes/ReturnOrderController/finish`, param, config),
    // 驳回
    reject: (param, config) => handleRequest(POST, `/api/luoto-mes/ReturnOrderController/reject`, param, config),
  },


  OutputConfirmation: {
    // 前段产出-根据设备获取信息
    getMachineDataForFinish: (param, config) => handleRequest(POST, `/api/luoto-mes/FrontEndController/getMachineDataForFinish`, param, config),

    // 前段产出确认
    Finish: (param, config) => handleRequest(POST, `/api/luoto-mes/FrontEndController/Finish`, param, config),
  },

  AdjustmentOrderApply: {
    // 1、扫描物料条码 
    scanConsumable: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=AdjustmentOrder_ListConsumable`, param, config),

    //调拨-提交
    submit: (param, config) => handleRequest(POST, `/api/luoto-mes/AdjustmentOrderController/submit`, param, config),
  },


  AdjustmentOrderList: {
    // 获取调拨单号列表
    ListAdjustmentOrder: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=AdjustmentOrder_ListAdjustmentOrder`, param, config),

    //根据调拨单获取详情
    listAdjustmentOrder: (param, config) => handleRequest(POST, `/api/luoto-mes/AdjustmentOrderController/listAdjustmentOrder`, param, config),

    //确认
    finish: (param, config) => handleRequest(POST, `/api/luoto-mes/AdjustmentOrderController/finish`, param, config),

    //确认
    reject: (param, config) => handleRequest(POST, `/api/luoto-mes/AdjustmentOrderController/reject`, param, config),
  },

  InventoryOrder: {
    // 获取盘点单号列表
    ListInventoryOrder: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=InventoryOrder_ListInventoryOrder`, param, config),

    //根据盘点单获取盘点详情列表
    listInventoryOrder: (param, config) => handleRequest(POST, `/api/luoto-mes/InventoryOrderController/listInventoryOrder`, param, config),

    //开始盘点
    inventory: (param, config) => handleRequest(POST, `/api/luoto-mes/InventoryOrderController/inventory`, param, config),

    //确认
    finish: (param, config) => handleRequest(POST, `/api/luoto-mes/InventoryOrderController/finish`, param, config),
  },

  QualityManagement: {
    getMachineForInspection: (param, config) => handleRequest(POST, `/api/luoto-mes/FirstInspectionController/getMachineForInspection`, param, config),

    // 2.创建检验任务
    startInspectionTask: (param, config) => handleRequest(POST, `/api/luoto-mes/FirstInspectionController/startInspectionTask`, param, config),

    // 现存任务编码
    getInspectionTaskList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/getInspectionTaskList', param, config),

    // 任务维护界面 - 取消任务
    cancelTask: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/inspectionTask/cancelTask', param, config),

    // 扫描样品条码：
    getLotData: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/getLotData', param, config),

    // getLotDataNew: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/getLotDataNew', param, config),

    // finishSampleNew: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/finishSampleNew', param, config),

    // 取样完成
    finshSample: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/finshSample', param, config),

    // 已取样信息
    // getFinshSampleList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/getFinshSampleList', param, config),

    // 已取样信息
    // cancel: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/inspectionTaskLot/cancel', param, config),


    /*检验执行*/
    // 获取任务编码
    getTaskNoList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/paramInput/getTaskNoList', param, config),

    // 样品条码
    getLotNameList: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/paramInput/getLotNameList', param, config),

    // 执行完成
    paramInputSave: (param, config) => handleRequest(POST, '/api/luoto-mes/FirstInspectionController/paramInput/save', param, config),

    // 首检参数录入界面 -参数录入明细
    getInspectiontemplatedetails: (param, config) => handleRequest(GET, '/api/luoto-mes/FirstInspectionController/paramInput/getInspectiontemplatedetails', param, config),

  },

  Labeling: {
    GetLotInfo: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=BindOrUnBind_GetLotInfo`, param, config),

    // changeLotName
    changeLotNameg: (param, config) => handleRequest(POST, `/api/luoto-mes/LotMgtController/changeLotNameg`, param, config),

    // BarcodeBinding
    BarcodeBinding: (param, config) => handleRequest(POST, `/api/luoto-mes/IotController/BarcodeBinding`, param, config),

    // printlabel
    printLabel: (param, config) => handleRequest(POST, `/api/luoto-mes/PrintAssignApi/printLabel`, param, config),
  },

  PDARetrospectiveReport: {
    // 查询
    just: (param, config) => handleRequest(POST, `/api/getech-lt-board/retrospectReport/just`, param, config),
    // 正向
    forwardJust: (param, config) => handleRequest(POST, `/api/getech-lt-board/retrospectReport/forwardJust`, param, config),
    //  反向
    reverseJust: (param, config) => handleRequest(POST, `/api/getech-lt-board/retrospectReport/reverseJust`, param, config),

    // 过站记录
    retrospectLotHist: (param, config) => handleRequest(POST, `/api/getech-lt-board/WipReportController/retrospectLotHist`, param, config),
    // 物料信息
    consumeMaterialInfo: (param, config) => handleRequest(POST, `/api/getech-lt-board/WipReportController/consumeMaterialInfo`, param, config),
    // 产品参数
    productParamInfo: (param, config) => handleRequest(POST, `/api/getech-lt-board/WipReportController/productParamInfo`, param, config),
    // 设备参数
    machineParamInfo: (param, config) => handleRequest(POST, `/api/getech-lt-board/WipReportController/machineParamInfo`, param, config),
    // 缺陷信息
    reasonCode: (param, config) => handleRequest(POST, `/api/getech-lt-board/WipReportController/reasonCode`, param, config),
    // 首检
    lotFirstInspectionInfo: (param, config) => handleRequest(POST, `/api/getech-lt-board/WipReportController/lotFirstInspectionInfo`, param, config),
  },
  BoxingExecution: {
    // 工单下拉
    GetPackingWorkOrderList: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetPackingWorkOrderList`, param, config),
    // 工单下拉
    GetMachineListForPacking: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetMachineListForPacking`, param, config),
    // 箱号下拉：
    GetOpenPackingByPrinted: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetOpenPackingByPrinted`, param, config),

    // 装箱信息
    GetPacking: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetPacking`, param, config),

    // 选择工单带出包装信息：
    getPackingRuleByWorkOrder: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/getPackingRuleByWorkOrder`, param, config),
    // 物料齐套校验：
    checkFeedingMaterial: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/checkFeedingMaterial`, param, config),
    // 申请箱号：
    generatorPacking: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/generatorPacking`, param, config),
    // 扫描成品条码：
    checkPackingBox: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/checkPackingBox`, param, config),
    // 扫描成品条码：
    savePackingInfoForBatch: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/savePackingInfoForBatch`, param, config),
    // 关厢
    closePacking: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/closePacking`, param, config),


    // 打印设备：
    GetPackingPrintedDot: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetPackingPrintedDot`, param, config),
    // 列表
    GetPackingInfo: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetPackingInfo`, param, config),
    // 移除
    unPackingBox: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/unPackingBox`, param, config),
  },
  BarcodeBinding: {
    // 物流码&箱码绑定
    bindingCode: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/bindingCode`, param, config),
  },
  ThreeCodeToOne: {
    // 获取包装设备
    GetPackMachine: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetPackMachine`, param, config),
    //获取物料消耗
    GetPackMachineFeedingmaterial: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetPackMachineFeedingmaterial`, param, config),
    //上料
    FeedingReport: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/FeedingReport`, param, config),
    //下料
    UnloadReport: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/UnloadReport`, param, config),
    //获取箱内信息
    getPackBoxInf: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=getPackBoxInf`, param, config),
    //获取详细信息
    getPackingInfoList: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=getPackingInfoList`, param, config),
    //获取装箱信息
    getPackInfByCusCode: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=getPackInfByCusCode`, param, config),
    //产品替换
    productReplace: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/productReplace`, param, config),
    //产品解绑
    productUnBind: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/productUnBind`, param, config),
    //产品解绑-整箱释放
    productUnBindAll: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/productUnBindAll`, param, config),

    //获取设备
    ScanMachineName: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=LotStation_ScanMachineName`, param, config),
    //获取工序和工单
    scanMachineNameInfo: (param, config) => handleRequest(POST, `/api/luoto-mes/MachineController/scanMachineName`, param, config),
    //获取载具信息
    scanDurableName: (param, config) => handleRequest(POST, `/api/luoto-mes/DurableController/scanDurableName`, param, config),
    //验证条码是否存在
    ScanLotName: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=LotStation_ScanLotName`, param, config),
    //一线放行
    throughOut: (param, config) => handleRequest(POST, `/api/luoto-mes/LotStationController/throughOut`, param, config),
    //包装查询
    packQuery: (param, config) => handleRequest(POST, `/api/getech-lt-board/retrospectReport/packQuery`, param, config),

    //获取包装配置
    getPackingConfig: (param, config) => handleRequest(POST, `/api/luoto-mes/packingconfig/getList`, param, config),
    reservConfig: (param, config) => handleRequest(POST, `/api/luoto-mes/packingconfig/add`, param, config),
    deleteCache: (param, config) => handleRequest(GET, `/api/luoto-mes/packingconfig/deleteCache`, param, config),
    //修改包装配置
    updatePackingConfig: (param, config) => handleRequest(POST, `/api/luoto-mes/packingconfig/updateList`, param, config),
    //获取PACK车间的NC号和其描述
    getPackNc: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=getPackNc`, param, config),
    //设备换型
    MachineChangeTask: (param, config) => handleRequest(POST, `/api/luoto-mes/MachineTaskController/add`, param, config),
    //根据设备获取工序
    getProcessOperationName: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=Get_ProcessOperationNameBymachinespec`, param, config),
    //根据PACK码获取绑定信息
    BindOrUnBind_GetSntrace: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=BindOrUnBind_GetSntrace`, param, config),
    //根据客户码获取绑定信息
    BindOrUnBind_GetSntrace_ByCurrentSn: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=BindOrUnBind_GetSntrace_ByCurrentSn`, param, config),
    //根据工单获取工单详细信息
    GetProductorderByName: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetProductorderByName`, param, config),
    //装箱打印
    printBoxingExecution: (param, config) => handleRequest(POST, `/api/luoto-mes/LotMgtController/printBoxingExecution`, param, config),
    //极片段设备已上料查询
    GetPackMachineFeedingmaterial: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=Common_G_GetPackMachineFeedingmaterial`, param, config),
    //修改数量
    updateQuantity: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/updateQuantity`, param, config),
    //下料
    UnloadReportIot: (param, config) => handleRequest(POST, `/api/luoto-mes/IotController/UnloadReport`, param, config),
    //获取包装规则
    GetPackingRuleList: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetPackingRuleList`, param, config),
    //解绑条码
    BarcodeUnBind: (param, config) => handleRequest(POST, `/api/luoto-mes/IotController/BarcodeUnBind`, param, config),
    //获取绑定条码（缓存）
    GetBarcodeBindCount: (param, config) => handleRequest(GET, `/api/luoto-mes/IotController/GetBarcodeBindCount`, param, config),
    //删除绑定条码（缓存）
    deleteBarcodeBindRecords: (param, config) => handleRequest(GET, `/api/luoto-mes/IotController/DeleteBarcodeBindRecords`, param, config),
    //获取静置列表
    getDurableStanding: (param, config) => handleRequest(POST, `/api/luoto-mes/durablestanding/getDurableStanding`, param, config),
    //上架
    packing: (param, config) => handleRequest(POST, `/api/luoto-mes/durablestanding/packing`, param, config),
    //下架
    unPacking: (param, config) => handleRequest(POST, `/api/luoto-mes/durablestanding/unPacking`, param, config),
  },
  // 拆解执行
  SnTraceController: {
    // 获取拆解执行列表
    getDecompositionExecution: (param, config) => handleRequest(GET, '/api/luoto-mes/SnTraceController/decompositionExecution', param, config),
    // 拆解执行内容修改
    putDecompositionExecution: (param, config) => handleRequest(PUT, '/api/luoto-mes/SnTraceController/decompositionExecution', param, config),
    // 拆解执行内容删除
    postDecompositionExecution: (param, config) => handleRequest(POST, '/api/luoto-mes/SnTraceController/decompositionExecution', param, config),
  },
  //电芯退库
  BatteryCellOfflineReturn: {
    getLotList: (param, config) => handleRequest(POST, '/api/luoto-mes/QueryDefApi/query?queryId=Get_G_LotList', param, config),
    batteryCellOfflineReturn: (param, config) => handleRequest(GET, '/api/luoto-mes/LotMgtController/batteryCellOfflineReturn', param, config),
	batteryCellOfflineReturnCheck: (param, config) => handleRequest(GET, '/api/luoto-mes/LotMgtController/batteryCellOfflineReturnCheck', param, config),
  },
	// 尾箱执行
	TailBoxDispose: {
		// 获取包装规格信息
		checkProductorderByCarrierName: (param, config) => handleRequest(POST, `/api/luoto-mes/PackingContriller/checkProductorderByCarrierName`, param, config),
		// 工单下拉
		GetPackingWorkOrderList2: (param, config) => handleRequest(POST, `/api/luoto-mes/QueryDefApi/query?queryId=GetPackingWorkOrderList2`, param, config),
		// 尾箱处置
		tailBoxDispose: (param, config) => handleRequest(POST, `/api/luoto-mes/ProductOrderApi/tailBoxDispose`, param, config),
	},
}