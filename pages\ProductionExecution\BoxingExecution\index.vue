<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">

        <u-form-item label="工单号" required borderBottom labelWidth="100">
          <!-- <view class="w100x flex right" @click="checkSelect('productOrderName')">
            <view v-if="model.productOrderName">{{ $utils.filterObjLabel(dicts.productOrderNameList,
              model.productOrderName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'productOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view> -->

          <view class="w100x flex right">
            <zqs-select :multiple="false" :list="dicts.productOrderNameList" label-key="label" value-key="value"
              placeholder=" 请选择" title="工单编码/产品名称" v-model="model.productOrderName"></zqs-select>
          </view>
        </u-form-item>

        <u-form-item label="成品编码" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.consumableSpecDesc) }}
          </view>
        </u-form-item>
        <view v-show="isBarcodeVisible">
          <u-form-item label="装箱规格" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ model.specification }}
            </view>
          </u-form-item>

          <u-form-item label="计划数量" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ model.createdQuantity }}
            </view>
          </u-form-item>
          <u-form-item label="工单完工数" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ model.finishedQuantity }}
            </view>
          </u-form-item>
          <u-form-item label="箱内产品总和" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ model.qty }}
            </view>
          </u-form-item>
          <u-form-item label="应申请箱数" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ model.planQty }}
            </view>
          </u-form-item>
          <u-form-item label="已申请箱数" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ model.proQty }}
            </view>
          </u-form-item>
        </view>
        <u-form-item label="设备编码" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('machineName')">
            <view v-if="model.machineName">{{ $utils.filterObjLabel(dicts.machineNameList, model.machineName) }}
            </view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'machineName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
        <view v-show="isBarcodeVisible">
          <u-form-item label="箱号" borderBottom labelWidth="100">
            <view class="w100x flex right">
              <view class="flex right flex1" @click="checkSelect('carrierName')">
                <view v-if="model.carrierName">{{ $utils.filterObjLabel(dicts.carrierNameList, model.carrierName) }}
                </view>
                <view class="c_c0c4cc" v-else>请选择</view>
                <view class="ml5"
                  :style="{ transform: select && selectType === 'carrierName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                  <u-icon name="arrow-down"></u-icon>
                </view>
              </view>
              <view class="flex hcenter">
                <u-button type="success" text="申请箱号" :disabled="!model.machineName" @click="applyBox"></u-button>
              </view>
            </view>
          </u-form-item>
        </view>
        <view class="ml5 fs16 mt20" style="color: #409eff"> 条码扫描 </view>

        <u-form-item label="条码" borderBottom labelWidth="100">
          <input ref="barcodeInput" v-model.trim="model.barcode" :focus="isFocus" border="none" @focus="handleBarcodeInputFocus"
            @blur="handleBarcodeInputBlur" inputmode="none" style="width: 100%;" placeholder="请扫描或输入客户码"></input>
          <view class="iconfont icon-saoma" @click="scan('barcode')"></view>
        </u-form-item>

        <u-form-item label="装箱数量" labelWidth="100">
          <view class="w100x flex right"> {{ list.length }}/{{ model.maxQuantity || 0 }} </view>
        </u-form-item>

        <view v-show="isBarcodeVisible">
          <view v-if="photoUrl" class="ml5 fs16 mt20" style="color: #409eff"> 图片 </view>
          <view v-if="photoUrl" style="margin-top: 20px;">
            <image :src="photoUrl" />
          </view>
        </view>
        <!-- <u-form-item label="打印设备" labelWidth="100">
          <view class="w100x flex right">
            <view class="flex right flex1" @click="checkSelect('printdot')">
              <view v-if="model.printdot">{{ $utils.filterObjLabel(dicts.printdotList, model.printdot) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" :style="{ transform: select && selectType === 'printdot' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
            <view class="flex hcenter">
              <u-button type="success" text="关箱打印" :disabled="!model.carrierName" @click="closePack"></u-button>
            </view>
          </view>
        </u-form-item> -->
        <view class="flex center">
          <u-icon :name="isBarcodeVisible ? 'arrow-up' : 'arrow-down'" @click="toggleBarcodeVisible"
            color="#409EFF"></u-icon>
        </view>

      </u--form>
      <view class="ml5 fs16 mt20" style="color: #409eff"> 明细列表 </view>

      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">条码号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">坐标</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 80rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.barcode }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 pl4 pr4 pt2 pb2">{{ ele.coord }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
              <view class="w80x" @click="deleteItem(ele, index)">
                <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>
      </view>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
        @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-datetime-picker ref="datetimePicker" v-model="selfRegisterTime" mode="date" closeOnClickOverlay
        @cancel="timeShow = false" @close="timeShow = false" :show="timeShow" :formatter="formatter" :maxDate="nowDate"
        @confirm="pickConfirm('time', $event)" visibleItemCount="5" itemHeight="68"></u-datetime-picker>

    </view>
    <view class="flex betweeen mt5">
      <u-button type="success" text="拍照" :disabled="!model.carrierName" @click="handleTakePhoto"></u-button>
      <u-button style="margin-left: 10%;" type="success" text="关箱" :disabled="!model.carrierName"
        @click="closePack"></u-button>
    </view>


  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import _ from "lodash";
import useNls from "@/mixins/useNls";
import PrintPackageMixin from "@/mixins/printPackageMixin";
import uploadFileService from "@/http/uploadFileService.js"

export default {
  mixins: [useNls, PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.changebarcode = this.$debounce(this.changebarcode, 100)
    this.changeproductOrderName == this.$debounce(this.changeproductOrderName, 100)
    this.keepFocus = this.$debounce(this.keepFocus, 100)
    return {
      nowDate: Number(new Date()),
      selfRegisterTime: Number(new Date()),
      timeShow: false, // 日期弹窗
      isBarcodeVisible: true, // 初始化展开状态
      photoUrl: '',//图片地址
      isFocus: true,
      shouldSelectAll: false, // 控制是否应该全选文本的标志
      isUserTyping: false, // 标记用户是否正在手动输入
      pageTitle: '',
      modeVisible: false,
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
        searchTitle: '查询结果'
      },
      dicts: {
        productOrderNameList: [], // 工单编码
        machineNameList: [], // 设备编码
        carrierNameList: [], // 箱号
        printdotList: [],  // 打印设备
      },
      list: [],
      // list: Array.from({ length: 20 }, (v, i) => ({
      // isPrint: ['1'],
      // i: i
      // })),
      model: {},
      columns: [],
      select: false,
      selectType: '',
    }
  },
  watch: {
    'model.barcode': {
      handler: _.debounce(function(val) {
        if (val) {
          this.changebarcode(val);
        }
      }, 300),
      immediate: true
    },
    'model.productOrderName': {
      handler(val) {
        const cleaned = (val || '').replace(/[\s\r\n\t]/g, '').trim();
        this.changeproductOrderName(cleaned)
      }
    },
    'model.carrierName': {
      handler(value) {
        console.log('value', value)
        if (!value) return
        this.keepFocus()

      }
    }
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.init_GetPackingWorkOrderList()// 工单下拉：
    // this.init_GetPackingPrintedDot()// 打印设备下拉
    this.initModel()
  },
  created() {
    this.initModel()
  },
  onReady() {
    // 页面渲染完成后，设置全选标志
    this.shouldSelectAll = true;
  },
  methods: {
    hideKeyboard() {
      if (process.env.UNI_PLATFORM === 'h5') {
        // 如果是 web 平台，不执行
        return;
      }
      uni.hideKeyboard(); // 隐藏软键盘
    },
    // 处理条码输入框获得焦点事件
    handleBarcodeInputFocus() {
      this.hideKeyboard();
      this.isUserTyping = false;
      // 只有在特定情况下才全选文本
      if (this.shouldSelectAll || this.model.barcode) {
        this.selectAllBarcodeText();
        this.shouldSelectAll = false; // 重置标志
      }
    },
    // 处理条码输入框失去焦点事件
    handleBarcodeInputBlur() {
      this.isUserTyping = false;
    },
    // 全选条码输入框文本
    selectAllBarcodeText() {
      if (this.$refs.barcodeInput && this.model.barcode && !this.isUserTyping) {
        // 在下一个tick中执行，确保DOM已更新
        this.$nextTick(() => {
          try {
            // 对于uni-app的input组件，使用setSelectionRange方法
            if (this.$refs.barcodeInput.setSelectionRange) {
              this.$refs.barcodeInput.setSelectionRange(0, this.model.barcode.length);
            } else if (this.$refs.barcodeInput.$el) {
              // 如果是组件包装的input，尝试访问原生DOM元素
              const inputEl = this.$refs.barcodeInput.$el.querySelector('input') || this.$refs.barcodeInput.$el;
              if (inputEl && inputEl.setSelectionRange) {
                inputEl.setSelectionRange(0, this.model.barcode.length);
              }
            }
          } catch (error) {
            console.log('选择文本失败:', error);
          }
        });
      }
    },
    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              carrierName: this.model.carrierName,
              barcodeList: [].concat(item.barcode)
            }
            this.$service.BoxingExecution.unPackingBox(params).then(res => {
              if (res.success) {
                this.$Toast('解绑箱号成功')
                this.list.splice(index, 1)
                this.keepFocus()
              }
            })
          }
          if (res.cancel) { }
        },
      })
    },
    toggleBarcodeVisible() {
      this.modeVisible = false;
      this.isBarcodeVisible = !this.isBarcodeVisible;
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'productOrderName':
          this.columns = this.dicts.productOrderNameList
          break;
        case 'machineName':
          this.columns = this.dicts.machineNameList
          break;
        case 'carrierName':
          this.columns = this.dicts.carrierNameList
          break;
        case 'printdot':
          this.columns = this.dicts.printdotList
          break;
        default:
          break;
      }
    },
    async pickConfirm(type, e) {
      this.timeShow = false
      switch (type) {
        case 'time':
          this.model.time = this.$dayjs(e.value).format('YYYY-MM-DD')

          this.list = []
          let params = {
            productOrderName: this.model.productOrderName,
            printedDot: this.model.machineName,
            carrierType: 'Box',
            productSpecName: this.model.consumableSpecName,
            time: this.model.time
          }
          let res = await this.$service.BoxingExecution.generatorPacking(params)
          let curNum = res.datas[0].quantity + 1  // quantity之前数量  装入 +1
          this.model.position = Math.ceil(curNum / res.datas[0].loadPreLayer)
          await this.init_GetOpenPackingByPrinted() // 更新箱号列表
          await this.getPackingRuleByWorkOrder(), // 获取包装规格信息
            await this.init_GetMachineListForPacking(), // 获取设备编码
            this.GetProductorderByName() // 获取工单信息
          this.model.carrierName = res.datas[0].carrierName
          break;
        default:
          break;
      }
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },
    async selectFirm(e) {
      console.log('e', e)
      this.$set(this.model, this.selectType, e.value[0].value)
      if (this.selectType == 'productOrderName') {
        await this.getPackingRuleByWorkOrder(), // 获取包装规格信息
          await this.init_GetMachineListForPacking(), // 获取设备编码
          this.GetProductorderByName() // 获取工单信息
        this.model.machineName = ''
        this.model.carrierName = ''

      }
      if (this.selectType == 'machineName') {
        this.model.position = ''
        this.model.carrierName = undefined
        this.model.printdot = undefined
        this.list = []
        uni.showModal({
          title: '提示',
          content: '是否选择作业[' + this.model.productOrderName + ']工单[' + this.model.consumableSpecName + ']物料',
          cancelText: '取消',
          confirmText: '确认',
          cancelColor: '#666',
          confirmColor: '#409eff',
          success: (res) => {
            if (res.confirm) {

              this.getProcessOperationName()//获取工序
            }
            if (res.cancel) {
              this.model.machineName = ''
              this.dicts.carrierNameList = []
            }
          },
        })



      }
      if (this.selectType == 'carrierName') {
        this.GetPacking()
        this.GetPackingInfo()
      }
      this.select = false
    },
    GetPacking() {
      let params = {
        carrierName: this.model.carrierName,
      }
      this.$service.BoxingExecution.GetPacking(params).then(res => {
        let curNum = res.datas[0].Quantity
        this.model.position = Math.ceil(curNum / res.datas[0].loadPreLayer) // 更新位置
      })
    },
    async MachineChangeTask() {

      let data = {
        machineName: this.model.machineName,
        machineTaskType: 'MachineChangeTask',
        productOrderName: this.model.productOrderName,
        processOperationName: this.model.processOperationName,
      }
      let res = await this.$service.ThreeCodeToOne.MachineChangeTask(data)
      if (res.success) {
        this.checkFeedingMaterial() // 检验
        this.init_GetOpenPackingByPrinted()// 箱号
      }
    },
    async getProcessOperationName() {
      let data = {
        machineName: this.model.machineName,
      }
      let res = await this.$service.ThreeCodeToOne.getProcessOperationName(data)
      if (res.success) {
        this.model.processOperationName = res.datas[0].processOperationName
        this.MachineChangeTask()//设备换型
      }

    },
    async getPackingRuleByWorkOrder() {
      let params = {
        productOrderName: this.model.productOrderName,
      }
      await this.$service.BoxingExecution.getPackingRuleByWorkOrder(params).then(res => {
        this.model.consumableSpecName = res.datas[0].consumableSpecName
        this.model.consumableSpecDesc = res.datas[0].consumableSpecDesc
        this.model.maxQuantity = res.datas[0].maxQuantity
        this.model.specification = `${res.datas[0].layerCount} x ${res.datas[0].loadPreLayer}`
      })
    },
    GetProductorderByName() {
      let params = {
        productOrderName: this.model.productOrderName,
      }
      this.$service.ThreeCodeToOne.GetProductorderByName(params).then(res => {
        this.model.createdQuantity = res.datas[0].createdQuantity
        this.model.finishedQuantity = res.datas[0].finishedQuantity
        this.model.qty = res.datas[0].qty
        this.model.proQty = res.datas[0].proQty

        this.model.planQty = Math.ceil(this.model.createdQuantity / this.model.maxQuantity);
      })
    },
    checkFeedingMaterial() {
      try {
        let params = {
          productOrderName: this.model.productOrderName,
          machineName: this.model.machineName,
        }
        this.$service.BoxingExecution.checkFeedingMaterial(params).then(res => {
        })
      } catch (error) {
        this.model.machineName = ''
      }
    },

    async changebarcode(value) {
      let barcode = value

      if (!value) return
      if (this.list.length == this.model.maxQuantity) {
        this.$Toast('已达到装箱最大数量')
        return
      }
      try {
        let params = {
          barcode: barcode,
          carrierType: 'Box',
          codeType: 'FERT',
          carrierName: this.model.carrierName,
          consumableSpecName: this.model.consumableSpecName,
          productOrderName: this.model.productOrderName,
          printedDot: this.model.machineName,
        }
        let res1 = await this.$service.BoxingExecution.checkPackingBox(params)
        if (res1.success) {
          let params2 = {
            carrierType: 'Box',
            carrierName: this.model.carrierName,
            packingInfoList: [
              {
                barcode: barcode,
                position: this.model.position,
                carrierName: this.model.carrierName,
                productOrderName: this.model.productOrderName,
                codeType: 'FERT',
                consumableSpecName: this.model.consumableSpecName
              }
            ]
          }
          let res2 = await this.$service.BoxingExecution.savePackingInfoForBatch(params2)
          if (res2.success) {
            this.$Toast('操作成功')
            this.model.barcode = ''
            this.keepFocus()
            setTimeout(() => {
              this.GetPackingInfo()
            }, 1000);
          }
        }


      } catch (error) {
        this.keepFocus()
      }
    },
    async changeproductOrderName(val) {
      if (!val) {
        return
      }
      await this.getPackingRuleByWorkOrder(), // 获取包装规格信息
        await this.init_GetMachineListForPacking(), // 获取设备编码
        this.GetProductorderByName() // 获取工单信息
      this.model.machineName = ''
      this.model.carrierName = ''
    },
    initModel() {
      this.model = {
        productOrderName: '', // 工单编码
        consumableSpecName: '',  // 成品编码
        consumableSpecDesc: '', // 成品名称
        position: '', // 层级
        specification: '', // 规格型号
        machineName: '', // 设备号
        carrierName: '', // 箱号
        barcode: '', // 条码
        maxQuantity: '', // 装箱数量
        printdot: '', // 打印设备
        createdQuantity: '',//计划数量
        finishedQuantity: '',//工单完工数
        qty: '',// 箱内产品总和
        proQty: '',//已申请箱数
        planQty: '',//应申请箱数
        photo: '',//图片
        time: '',//时间
      }
      // this.dicts.productOrderNameList = []
      this.dicts.machineNameList = []
      this.list = []
    },

    GetPackingInfo() {
      let params = {
        carrierName: this.model.carrierName
      }
      this.$service.BoxingExecution.GetPackingInfo(params).then(res => {
        this.list = res.datas;
      })
    },
    keepFocus() {
      this.modeVisible = false;
      this.shouldSelectAll = true; // 设置全选标志

      if (this.model.carrierName) {
        this.isFocus = false;
        this.$nextTick(() => {
          this.isFocus = true;
          this.modeVisible = true;
        });
      } else {
        // 即使没有carrierName，也要确保条码输入框获得焦点
        this.isFocus = false;
        this.$nextTick(() => {
          this.isFocus = true;
        });
      }

    },
    async applyBox() {
      if (this.model.proQty >= this.model.planQty) {
        uni.showModal({
          title: '提示',
          content: '继续申请箱号，会超过应申请箱号上限，是否继续申请？',
          cancelText: '取消',
          confirmText: '确认',
          cancelColor: '#666',
          confirmColor: '#409eff',
          success: async (res) => {

            if (res.confirm) {
              this.timeShow = true
            }
            if (res.cancel) { }
          },
        })
      } else {
        this.timeShow = true
      }

    },
    // 请求相机权限（仅 App 必需）
    requestCameraPermission(callback) {
      // #ifdef APP-PLUS
      plus.android.requestPermissions(
        ['android.permission.CAMERA'],
        (resultObj) => {
          const result = resultObj.granted.includes('android.permission.CAMERA');
          if (result) {
            callback && callback();
          } else {
            this.$Toast('请在设置中开启摄像头权限');
          }
        },
        (error) => {
          console.error('权限请求失败', error);
          this.$Toast('摄像头权限请求失败');
        }
      );
      // #endif

      // 非APP直接执行
      // #ifndef APP-PLUS
      callback && callback();
      // #endif
    },
    // 拍照并上传
    handleTakePhoto() {
      this.requestCameraPermission(() => {
        uni.chooseImage({
          count: 1,
          sourceType: ['camera'],
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            this.photoUrl = tempFilePath;

            this.uploadImage(tempFilePath);
          },
          fail: (err) => {
            console.error('拍照失败', err);
            this.$Toast('拍照失败');
          }
        });
      });
    },
    // 上传图片
    async uploadImage(file) {

      uploadFileService({
        filePath: file, // 选中的临时文件路径
        url: '/api/poros-oss/file/upload',
        formData: {
          bucketName: 'ltdtmes',
          expireDate: 0
        },
        config: {
          showLoading: true
        }
      }).then(res => {
        this.model.photo = res.data
        this.$Toast('上传成功')
        console.log('上传成功', res)
      }).catch(err => {
        console.log('上传失败', err)
      })

    },
    async closePack() {

      if (!this.model.carrierName) {
        this.$Toast('请先选择箱号!')
        return
      }
      if (!this.model.photo) {
        this.$Toast('请先拍照!')
        return
      }
      if (this.list.length === 0) {
        this.$Toast('0条码不允许装箱!')
        return
      }
      if (this.list.length < this.model.maxQuantity) {
        uni.showModal({
          title: '提示',
          content: `当前箱号[${this.model.carrierName}]未装满,是否确认关箱打印?`,
          cancelText: '取消',
          confirmText: '确认',/* 只可以4个字 */
          cancelColor: '#666',
          confirmColor: '#409eff',
          success: async (res) => {
            if (res.confirm) {
              let params = {
                carrierName: this.model.carrierName,
                photo: this.model.photo,
                lotName: (this.list || []).map(item => item.barcode),

                productOrderName: this.model.productOrderName
              }
              let res = await this.$service.BoxingExecution.closePacking(params)
              //打印
              let data = {
                consumableSpecName: this.model.consumableSpecName,
                consumableSpecDesc: this.model.consumableSpecDesc,
                carrierName: this.model.carrierName,
                machineName: this.model.machineName,
                qty: this.list.length,
                flag: 'true'
              }
              this.printBoxingExecution(data)
              this.initModel()
            }
            if (res.cancel) { }
          },
        })
      } else {
        let params = {
          carrierName: this.model.carrierName,
          photo: this.model.photo,
          lotName: (this.list || []).map(item => item.barcode).filter(barcode => barcode),
          productOrderName: this.model.productOrderName
        }
        let res = await this.$service.BoxingExecution.closePacking(params)
        //打印
        let data = {
          consumableSpecName: this.model.consumableSpecName,
          consumableSpecDesc: this.model.consumableSpecDesc,
          carrierName: this.model.carrierName,
          machineName: this.model.machineName,
          qty: this.list.length,
          flag: 'true'
        }
        this.printBoxingExecution(data)
        this.initModel()
        this.keepFocus()
      }
    },

    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    init_GetPackingWorkOrderList() {
      this.$service.BoxingExecution.GetPackingWorkOrderList().then(res => {
        this.dicts.productOrderNameList = res.datas.map(item => ({
          label: `${item.productOrderName}/${item.productOrderNameTxt}`,
          value: item.productOrderName,
        }))
      })
    },

    async init_GetMachineListForPacking() {
      let params = {
        productOrderName: this.model.productOrderName
      }
      await this.$service.BoxingExecution.GetMachineListForPacking(params).then(res => {
        this.dicts.machineNameList = res.datas.map(item => ({
          label: `${item.machineName}/${item.description}`,
          value: item.machineName,
        }))
      })
    },
    init_GetOpenPackingByPrinted() {
      let params = {
        productOrderName: this.model.productOrderName,
        machineName: this.model.machineName,
      }
      this.$service.BoxingExecution.GetOpenPackingByPrinted(params).then(res => {
        this.dicts.carrierNameList = res.datas.map(item => ({
          label: item.carrierName,
          value: item.carrierName,
        }))
      })
    },
    init_GetPackingPrintedDot() {
      this.$service.BoxingExecution.GetPackingPrintedDot().then(res => {
        this.dicts.printdotList = res.datas.map(item => ({
          label: item.printDesc,
          value: item.machineName,
        }))
      })
    },
    printBoxingExecution(param) {
      this.$service.ThreeCodeToOne.printBoxingExecution(param).then(res => {
        if (param.flag == 'true') {
          uni.showLoading({
            title: '打印中...',
            mask: true
          });
          if (res.success) {
            uni.hideLoading()
            this.$Toast('打印成功')
          }
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'barcode':
          this.model.barcode = 'C1Z001002'
          // 扫描后全选文本
          this.$nextTick(() => {
            this.selectAllBarcodeText();
          });
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          const raw = res.result || '';
          const cleaned = raw.replace(/\s+/g, '').trim(); // 清洗：去掉空格、换行、制表符
          this.$set(this.model, key, cleaned)
          // 扫描后全选文本
          if (key === 'barcode') {
            this.$nextTick(() => {
              this.selectAllBarcodeText();
            });
          }
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
