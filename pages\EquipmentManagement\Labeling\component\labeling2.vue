<template>
  <view class="myContainerPage pl5 pr5 pb5" :class="{ 'error-shake': showErrorEffect }">
    <view class="mb10 br10 bc_fff pa10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="PACK码" borderBottom required labelWidth="100">
          <input v-model="model.sourceLotName" :focus="focus_sourceLotName" border="none" @focus="hideKeyboard"
            inputmode="none" style="width: 100%;" placeholder="请扫描"></input>
          <!-- 清除按钮 -->
          <view v-if="model.sourceLotName" class="close-icon-wrapper">
            <u-icon name="close" class="close-icon" color="#999" size="20" @click="clearField('sourceLotName')" />
          </view>
          <view class="iconfont icon-saoma" @click="scan('sourceLotName')"></view>
        </u-form-item>
        <u-form-item label="客户码" borderBottom required labelWidth="100">
          <input v-model="model.targetLotName" :focus="focus_targetLotName" border="none" @focus="hideKeyboard"
            :inputmode="focus_sourceLotName ? 'none' : 'none'" style="width: 100%;" placeholder="请扫描"></input>
          <!-- 清除按钮 -->
          <view v-if="model.targetLotName" class="close-icon-wrapper">
            <u-icon name="close" class="close-icon" color="#999" size="20" @click="clearField('targetLotName')" />
          </view>
          <view class="iconfont icon-saoma" @click="scan('targetLotName')"></view>
        </u-form-item>

        <u-form-item label="客户码二" borderBottom required labelWidth="100">
          <input v-model="model.targetLotName2" :focus="focus_targetLotName2" border="none" @focus="hideKeyboard"
            :inputmode="focus_sourceLotName ? 'none' : 'none'" style="width: 100%;" placeholder="请扫描"></input>
          <!-- 清除按钮 -->
          <view v-if="model.targetLotName2" class="close-icon-wrapper">
            <u-icon name="close" class="close-icon" color="#999" size="20" @click="clearField('targetLotName2')" />
          </view>
          <view class="iconfont icon-saoma" @click="scan('targetLotName2')"></view>
        </u-form-item>

        <u-form-item label="物料编码" borderBottom labelWidth="120">
          <view class="w100x flex right">
            <!-- {{ $utils.optionShowConfig(model.productSpecName, model.productSpecDesc) }} -->
            {{ model.productSpecName }}
          </view>
        </u-form-item>

        <u-form-item label="物料名称" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productSpecDesc }}
          </view>
        </u-form-item>

        <u-form-item label="生产工单" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productOrderName }}
          </view>
        </u-form-item>

        <u-form-item label="质量状态" labelWidth="120">
          <view class="w100x flex right">
            {{ $utils.filterObjLabel(dicts.lotGradeList, model.lotGrade) }} {{ model.lotGrade2 }}
          </view>
        </u-form-item>
      </u--form>
    </view>
    <view v-if="list.length > 0" class="ml5 fs16 mt20" style="color: #409eff; display: flex; align-items: center;">
      <view>【已绑定总数:{{ list.length }}】</view>
      <view class="ml10" @click="handleClear">【清除】</view>
    </view>
    <view class="myContainer">
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
        @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>



      <view class="listContainer ma10">
        <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>序号</view>
              <view>{{ list.length - index }}</view>
            </view>

            <view class="flex between h40 hcenter c_999">
              <view>PACK码</view>
              <view>{{ ele.serialNo }} </view>
            </view>

            <view class="flex between h40 hcenter c_999">
              <view>客户码</view>
              <view> {{ ele.parentSerialNo }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>时间</view>
              <view> {{ ele.localDateTime ? moment(ele.localDateTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </scroll-view>
      </view>
    </view>


    <view class="btnContainer" @click="submit">绑定</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import { USER_ID } from '@/utils/common/evtName.js';
import moment from 'moment';
import _ from "lodash";
import NoData from '@/components/NoData/noData';
import useNls from "@/mixins/useNls";

export default {
  name: 'binding',
  mixins: [useNls, ScrollMixin],
  props: {
    nlsMap: {
      type: Object,
      default: () => {
      }
    }
  },
  components: {
    NoData,
  },
  data() {
    this.changesourceLotName = this.$debounce(this.changesourceLotName, 1000)
    this.changetargetLotName = this.$debounce(this.changetargetLotName, 1000)
    this.changetargetLotName2 = this.$debounce(this.changetargetLotName2, 1000)
    return {
      showErrorEffect: '',
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      rulesTip: {
        sourceLotName: '当前条码不能为空',
        targetLotName: '粘贴条码不能为空',
      },
      model: {},
      columns: [],
      select: false,
      selectType: '',
      dicts: {
        lotGradeList: [], // 质量
      },
      focus_sourceLotName: true,
      focus_targetLotName: false,
      focus_targetLotName2: false,
      packingRuleList: [],
      checkCode: 0,
      list: [],

    };
  },
  watch: {
    'model.sourceLotName': {
      handler(val) {
        this.focus_targetLotName = false
        const cleaned = (val || '').replace(/[\s\r\n\t]/g, '').trim();
        this.model.sourceLotName = cleaned
        this.changesourceLotName(cleaned)
      }
    },
    'model.targetLotName': {
      handler(val) {
        this.focus_targetLotName2 = false
        const cleaned = (val || '').replace(/[\s\r\n\t]/g, '').trim();
        this.model.targetLotName = cleaned
        this.changetargetLotName(cleaned)
      }
    },
    'model.targetLotName2': {
      handler(val) {
        // this.focus_targetLotName = true
        //   this.focus_targetLotName2 = false
        this.focus_sourceLotName = false
        const cleaned = (val || '').replace(/[\s\r\n\t]/g, '').trim();
        this.model.targetLotName2 = cleaned
        this.changetargetLotName2(cleaned)
      }
    },
  },
  created() {
    this.initModel()
    this.GetBarcodeBindCount()
    this.getEnumValue('LotGrade', 'lotGradeList') // 质量
    this.GetPackingRuleList()
    // this.initSearchModel()
  },
  mounted() {
    this.$nextTick(() => {
      this.focus_sourceLotName = true
    })
  },
  methods: {
    hideKeyboard() {
      if (process.env.UNI_PLATFORM === 'h5') {
        // 如果是 web 平台，不执行
        return;
      }
      uni.hideKeyboard(); // 隐藏软键盘
    },
    clearField(field) {
      this.model[field] = '';
      const focusKey = `focus_${field}`;
      this[focusKey] = false;
      this.$nextTick(() => {
        this[focusKey] = true;
      });
    },
    moment,
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    initModel() {
      this.model = {
        sourceLotName: '',// 当前条码
        targetLotName: '',// 粘贴条码
        targetLotName2: '',
        productSpecName: '',// 粘贴条码
        productSpecDesc: '',// 粘贴条码
        productOrderName: '',// 生产工单
        lotGrade: '',// 质量状态
        lotGrade2: '',
        lotNameConsistent: null,
      }

      this.packSnRule = {}
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'params1':
          // this.columns = this.dicts.params1List
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },
    async GetPackingRuleList() {
      let res1 = await this.$service.ThreeCodeToOne.GetPackingRuleList({})
      if (res1.success) {
        this.packingRuleList = res1.datas
      }
    },
    getConsumableSpecNameByValue(value, packingRuleList) {
      if (!value || !Array.isArray(packingRuleList)) return null;

      const match = packingRuleList.find(item => {
        if (!item.packSnRule) return false;

        // // 将规则中的 * 替换成任意长度的正则表达式 .*
        // const regexStr = '^' + item.packSnRule.replace(/\*/g, '.*') + '$';
        // const regex = new RegExp(regexStr); // 如果需要忽略大小写，可用 /.../i

        // return regex.test(value);
        return this.wildcardMatch(item.packSnRule, value);
      });

      if (match) {
         this.model.lotNameConsistent = match.lotNameConsistent;
         this.packSnRule = match;
         console.log("match.lotNameConsistent" + match.lotNameConsistent);
       }

      return match ? match.consumableSpecName : null;
    },
    // getConsumableSpecNameByTargetLotName(value, packingRuleList, productSpecName) {
    //   if (!value || !Array.isArray(packingRuleList)) return null;

    //   const match = packingRuleList.find(item => {
    //     if (!item.customerSnRule) return false;

    //     // 安全地转义正则中的特殊字符
    //     let safeRule = this.escapeRegExp(item.customerSnRule);
    //     // 再将 * 替换成 .*
    //     const regexStr = '^' + safeRule.replace(/\\\*/g, '.*') + '$';
    //     const regex = new RegExp(regexStr); // 可以加 i 忽略大小写
    //     const start = performance.now();
    //     const result = regex.test(value);
    //     const end = performance.now();

    //     const timeCost = end - start;
    //     if (timeCost > 100) {
    //       console.warn(`慢匹配警告: ${regexStr} 匹配 ${value} 花费 ${timeCost.toFixed(2)} ms`);
    //     }
    //     return result;
    //   });
    //   if (!match) {
    //     return false
    //   }
    //   console.log("consumableSpecName", match.consumableSpecName)
    //   return match.consumableSpecName === productSpecName;
    // }
    getConsumableSpecNameByTargetLotName(value, packingRuleList, productSpecName) {
      if (!value || !Array.isArray(packingRuleList)) return null;

      const match = packingRuleList.find(item => {
        if (!item.customerSnRule) return false;

        return this.wildcardMatch(item.customerSnRule, value);
      });

      if (!match) return false;
      this.model.lotNameConsistent = match.lotNameConsistent;
      this.packSnRule = match;

      return match.consumableSpecName === productSpecName;
    },

    getConsumableSpecNameByTargetLotName2(value, packingRule) {
      if (!value || !packingRule) return false;
      return this.wildcardMatch(packingRule.secondCustomerCodeRule, value);
    },


    // 通配符匹配函数：支持 * 匹配任意字符（非正则）
    wildcardMatch(pattern, text) {
      const parts = pattern.split('*');
      // 长度是否匹配
      if (text.length !== pattern.length) {
        return false;
      }
      let position = 0;

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];

        if (part === '') continue;

        const idx = text.indexOf(part, position);
        if (idx === -1) return false;

        position = idx + part.length;
      }

      // 如果 pattern 不是以 * 结尾，确保匹配到末尾
      if (!pattern.endsWith('*') && position !== text.length) {
        return false;
      }

      // 如果 pattern 是以 * 开头，确保开头可以模糊
      if (!pattern.startsWith('*') && !text.startsWith(parts[0])) {
        return false;
      }

      return true;
    }

    ,
    // escapeRegExp(str) {
    //   return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // },
    // AK12345
    // 01DPBB3F0942222222222222
    // KH_TEST_1234567
    getConsumableSpecDescByValue(value, packingRuleList) {
      if (!value || !Array.isArray(packingRuleList)) return null;

      const match = packingRuleList.find(item => {
        if (!item.packSnRule) return false;

        // // 将规则中的 * 替换成任意长度的正则表达式 .*
        // const regexStr = '^' + item.packSnRule.replace(/\*/g, '.*') + '$';
        // const regex = new RegExp(regexStr); // 如果需要忽略大小写，可用 /.../i

        // return regex.test(value);

        return this.wildcardMatch(item.packSnRule, value);
      });

      return match ? match.consumableSpecDesc : null;
    },
    getLotNameConsistentByValue(value, packingRuleList) {
      if (!value || !Array.isArray(packingRuleList)) return null;

      const match = packingRuleList.find(item => {
        if (!item.packSnRule) return false;
        return this.wildcardMatch(item.packSnRule, value);
      });
      return match ? match.lotNameConsistent : null;
    },
    async changesourceLotName(value) {
      if (!value) return
      if (!value || value.length !== 24) {
        this.$Toast('请输入24位条码')
        return
      }

      if (value.substring(0, 5).toUpperCase() === "PACK1".toUpperCase()) {

        this.checkCode = 1
      } else {
        const code = parseInt(value[12]);
        this.checkCode = code === 4 ? 3 : [1, 2].includes(code) ? code : 0;
      }

      if (this.checkCode === 0) {
        this.$Toast('请输入正确条码')
        return
      }
      this.focus_targetLotName = true
      console.log('checkCode', this.checkCode)
      try {
        let params = {
          lotName: value,
        }
        let res1 = await this.$service.ThreeCodeToOne.BindOrUnBind_GetSntrace(params)
        if (res1.success) {
          if (res1.datas.length > 0) {
            this.initModel()
            // this.$Toast('该条码已绑定客户码[' + res1.datas[0].currentSn + ']')
            this.showErrorEffect = true
            uni.showModal({
              title: '错误提示',
              content: '该条码已绑定客户码[' + res1.datas[0].currentSn + ']',
              showCancel: true,
              cancelText: '取消',
              confirmText: '我知道了',
              success: (res) => {
                if (res.cancel) {
                  this.showErrorEffect = false // 取消关闭效果
                }
                this.showErrorEffect = false
                // confirm 点击可以保留效果或也关闭
                this.focus_sourceLotName = true

                this.focus_sourceLotName = false;
                this.$nextTick(() => {
                  this.focus_sourceLotName = true;
                });
              }
            })
            return
          }
        }
        if (this.checkCode != 2 && this.checkCode != 3) {
          let res = await this.$service.Labeling.GetLotInfo(params)
          if (res.datas.length > 0) {
            this.model.productSpecName = res.datas[0].productSpecName
            this.model.productSpecDesc = res.datas[0].productSpecDesc
            this.model.productOrderName = res.datas[0].productOrderName
            this.model.lotGrade = res.datas[0].lotGrade
            this.model.lotGrade2 = ''
            this.focus_targetLotName = true
          } else {
            this.focus_sourceLotName = true
            this.$Toast('条码不存在')
            this.initModel()
            return
          }
        } else {
          if (this.getConsumableSpecNameByValue(this.model.sourceLotName, this.packingRuleList) === null) {
            const hasRules = this.packingRuleList && this.packingRuleList.length > 0
            const errorMsg = hasRules ?
                '编码' + this.model.sourceLotName + '和维护的规则不匹配' :
                '编码' + this.model.sourceLotName+ '没有对应的规则'

            this.showErrorEffect = true
            uni.showModal({
              title: '错误提示',
              content: errorMsg,
              showCancel: true,
              cancelText: '取消',
              confirmText: '我知道了',
              success: (res) => {
                if (res.cancel) {
                  this.showErrorEffect = false // 取消关闭效果
                }
                this.showErrorEffect = false
                // confirm 点击可以保留效果或也关闭
                this.model.sourceLotName = ''
                this.focus_sourceLotName = true
              }
            })
            return
          } else {
            this.model.productSpecName = this.getConsumableSpecNameByValue(this.model.sourceLotName, this.packingRuleList)
            this.model.productSpecDesc = this.getConsumableSpecDescByValue(this.model.sourceLotName, this.packingRuleList)
            this.model.productOrderName = this.checkCode === 2 ? '二线产品' : '三线产品'
            this.model.lotGrade2 = this.checkCode === 2 ? '二线产品' : '三线产品'
          }
        }

        // 校验 lotNameConsistent 其他条码一致性校验 （针对所有pack码）
        if (this.getLotNameConsistentByValue(this.model.sourceLotName, this.packingRuleList) === null){
          uni.showModal({
            title: '错误提示',
            content: "条码需要绑定的客户码只有一条！",
            showCancel: true,
            cancelText: '取消',
            confirmText: '我知道了',
            success: (res) => {
              if (res.cancel) {
                this.showErrorEffect = false // 取消关闭效果
              }
              this.showErrorEffect = false
              // confirm 点击可以保留效果或也关闭
              this.model.sourceLotName = ''
              this.focus_sourceLotName = true
            }
          })
        }
      } catch (error) {
        this.focus_sourceLotName = true
        this.initModel()
      }
    },
    changetargetLotName(value) {

      if (!value) return
      if (!this.model.sourceLotName) {
        this.model.targetLotName = ''
        return this.$Toast('PACK码不能为空!')
      }
      this.focus_targetLotName2 = true
      if (!this.getConsumableSpecNameByTargetLotName(value, this.packingRuleList, this.model.productSpecName)) {
        const hasRules = this.packingRuleList && this.packingRuleList.length > 0
        const errorMsg = hasRules ?
            '编码' + value + '和维护的规则不匹配' :
            '编码' + value + '没有对应的规则'

        this.showErrorEffect = true
        uni.showModal({
          title: '错误提示',
          content: errorMsg,
          showCancel: true,
          cancelText: '取消',
          confirmText: '我知道了',
          success: (res) => {
            if (res.cancel) {
              this.showErrorEffect = false // 取消关闭效果
            }
            this.showErrorEffect = false
            // confirm 点击可以保留效果或也关闭
            this.model.targetLotName = ''
            this.focus_targetLotName = true
          }
        })
      }


      //验证其他条码一致性是否维护
      if (this.model.lotNameConsistent === null) {
        this.showErrorEffect = true
        uni.showModal({
          title: '错误提示',
          content: '编码' + this.model.targetLotName + '不存在第二客户码，请检查！',
          showCancel: true,
          cancelText: '取消',
          confirmText: '我知道了',
          success: (res) => {
            if (res.cancel) {
              this.showErrorEffect = false // 取消关闭效果
            }
            this.showErrorEffect = false
            // confirm 点击可以保留效果或也关闭
            this.model.targetLotName = ''
            this.focus_targetLotName = true
          }
        })
      }

    },
    changetargetLotName2(value) {

      if (!value) return
      if (!this.model.sourceLotName) {
        this.model.targetLotName = ''
        return this.$Toast('PACK码不能为空!')
      }
      if (!this.model.targetLotName) {
        this.model.targetLotName2 = ''
        return this.$Toast('客户码不能为空!')
      }
      console.log(this.model.lotNameConsistent);
      console.log(typeof this.model.lotNameConsistent);

      switch (this.model.lotNameConsistent) {
        case 0:
          console.log(this.packSnRule.secondCustomerCodeRule)
          if (this.packSnRule.secondCustomerCodeRule != null && this.packSnRule.secondCustomerCodeRule != '') {
            console.log(value)
            if (!this.getConsumableSpecNameByTargetLotName2(value, this.packSnRule)) {
              this.showErrorEffect = true
              uni.showModal({
                title: '错误提示',
                content: '客户码二' + value + '规则验证失败',
                showCancel: true,
                cancelText: '取消',
                confirmText: '我知道了',
                success: (res) => {
                  if (res.cancel) {
                    this.showErrorEffect = false // 取消关闭效果
                  }
                  this.showErrorEffect = false
                  // confirm 点击可以保留效果或也关闭
                  this.model.targetLotName2 = ''
                  this.focus_targetLotName2 = true
                }
              })
              return
            }
          }
          break;
        case 1:
          if (value != this.model.targetLotName) {
            this.showErrorEffect = true
            uni.showModal({
              title: '错误提示',
              content: '客户码和客户码二不一致',
              showCancel: true,
              cancelText: '取消',
              confirmText: '我知道了',
              success: (res) => {
                if (res.cancel) {
                  this.showErrorEffect = false // 取消关闭效果
                }
                this.showErrorEffect = false
                // confirm 点击可以保留效果或也关闭
                this.model.targetLotName2 = ''
                this.focus_targetLotName2 = true
              }
            })
            return
          }
          break;
        default:
          break;
      }


      this.submit()

    },

    async GetBarcodeBindCount() {
      this.list = []
      let params = {
        user: this.$getLocal(USER_ID),
      }
      let res = await this.$service.ThreeCodeToOne.GetBarcodeBindCount(params)
      if (res.success) {
        if (res.datas.length > 0) {

          this.list = res.datas.sort((a, b) => new Date(b.localDateTime) - new Date(a.localDateTime))

        }
      }

    },
    async handleClear() {
      let params = {
        user: this.$getLocal(USER_ID),
      }
      let res = await this.$service.ThreeCodeToOne.deleteBarcodeBindRecords(params)
      console.log(res.success)
      if (res.success) {
        this.GetBarcodeBindCount()
        this.$Toast('已清除')

      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }

      let formattedTimestamp = moment().format('YYYYMMDDHHmmssSSS');
      let randomNum = Array.from({ length: 5 }, () => Math.floor(Math.random() * 10)).join('')
      let transId = `${formattedTimestamp}.${randomNum}`
      let eventTime = moment().format('YYYY-MM-DD HH:mm:ss')

      let params = {
      }
      switch (this.checkCode) {
        case 1:
          params = {
            cellList: [
              {
                "serialNo": this.model.sourceLotName,
                "parentSerialNo": this.model.targetLotName,
                "parentSerialNo2": this.model.targetLotName2,
              }
            ],
            eventTime: eventTime,
            eventType: 'U',
            eventUser: this.$getLocal(USER_ID),
            factoryNo: "10011",
            lineName: "BZ1",
            transId: transId,
            workShopSection: "BZ1PACKBZ"
          }
          break
        case 2:
        case 3:

          if (this.getConsumableSpecNameByValue(this.model.sourceLotName, this.packingRuleList) === null) {
            const hasRules = this.packingRuleList && this.packingRuleList.length > 0
            const errorMsg = hasRules ?
                '编码' + this.model.sourceLotName + '和维护的规则不匹配' :
                '编码' + this.model.sourceLotName+ '没有对应的规则'

            this.showErrorEffect = true
            uni.showModal({
              title: '错误提示',
              content: errorMsg,
              showCancel: true,
              cancelText: '取消',
              confirmText: '我知道了',
              success: (res) => {
                if (res.cancel) {
                  this.showErrorEffect = false // 取消关闭效果
                }
                this.showErrorEffect = false
                // confirm 点击可以保留效果或也关闭
                this.model.sourceLotName = ''
              }
            })
          } else {
            params = {
              cellList: [
                {
                  "serialNo": this.model.sourceLotName,
                  "parentSerialNo": this.model.targetLotName,
                  "parentSerialNo2": this.model.targetLotName2,
                  "parentProductInfo": {
                    productName: this.getConsumableSpecNameByValue(this.model.sourceLotName, this.packingRuleList),
                    workShopSection: this.checkCode === 2 ? "BZ2QDPACK" : "BZ3QDPACK",
                    lineName: this.checkCode === 2 ? "BZ2" : "BZ3",
                  }
                }
              ],
              eventTime: eventTime,
              eventType: 'U',
              eventUser: this.$getLocal(USER_ID),
              factoryNo: "10011",
              lineName: this.checkCode === 2 ? "BZ2" : "BZ3",
              transId: transId,
              workShopSection: this.checkCode === 2 ? "BZ2QDPACK" : "BZ3QDPACK",
            }
          }


          break
        default:
          this.showErrorEffect = true
          uni.showModal({
            title: '错误提示',
            content: '第13位' + this.checkCode + ',无法判断产线',
            showCancel: true,
            cancelText: '取消',
            confirmText: '我知道了',
            success: (res) => {
              if (res.cancel) {
                this.showErrorEffect = false // 取消关闭效果
              }
              this.showErrorEffect = false
              // confirm 点击可以保留效果或也关闭
            }
          })
      }
      this.$service.Labeling.BarcodeBinding(params, { handleSucess: true }).then(res => {
        if (res.resultCode == 0) {
          this.GetBarcodeBindCount()
          this.initModel()
          this.$nextTick(() => {
            this.focus_sourceLotName = true
          })
          this.$Toast('绑定成功!')
        } else {
          // 启动“红边+抖动”动画
          this.showErrorEffect = true
          uni.showModal({
            title: '错误提示',
            content: res.resultMsg || '操作失败，请检查数据！',
            showCancel: true,
            cancelText: '取消',
            confirmText: '我知道了',
            success: (res) => {
              if (res.cancel) {
                this.showErrorEffect = false // 取消关闭效果
              }
              this.showErrorEffect = false
              this.focus_targetLotName = true
              // confirm 点击可以保留效果或也关闭
            }
          })
          // this.$Toast(res.resultMsg)
          this.focus_targetLotName = true
          this.model.targetLotName = ''
        }
      })
    },
    initSearchModel() {
      this.searchModel = {
        operateNo: '10001',
        trayName: null,
        pageNo: this.pageNumber,
        limit: 99999,
      }
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'sourceLotName':
          this.model.sourceLotName = 'C20240730164218527'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          const raw = res.result || '';
          const cleaned = raw.replace(/\s+/g, '').trim(); // 清洗：去掉空格、换行、制表符
          this.$set(this.formModel, key, cleaned)
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.myContainerPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100%;

  .myContainer {
    flex: 1;
    overflow-y: scroll;
  }
}

@keyframes alarmFlash {
  0% {
    box-shadow: 0 0 0px red;
  }

  50% {
    box-shadow: 0 0 20px red;
  }

  100% {
    box-shadow: 0 0 0px red;
  }
}

@keyframes shake {
  0% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  50% {
    transform: translateX(5px);
  }

  75% {
    transform: translateX(-5px);
  }

  100% {
    transform: translateX(0);
  }
}

.error-shake {
  animation: shake 0.4s ease-in-out, alarmFlash 1s infinite;
  border: 2px solid red;
  border-radius: 10px;
}



// .listPage {
//   display: flex;
//   flex-direction: column;
//   overflow: hidden;
//   width: 100vw;
//   height: 100%;
//   .topContainer {
//     flex-shrink: 0;
//   }

//   .listContainer {
//     flex: 1;
//     display: flex;
//     flex-direction: column;
//     overflow: hidden;
//     margin-top: 20rpx;
//     .table_header {
//       flex-shrink: 0;
//     }
//     .table_content {
//       flex: 1;
//       overflow-y: scroll;
//     }
//   }
// }
</style>
