<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="条码扫描" borderBottom labelWidth="100">
          <input v-model="model.lotName" :focus="isFocus" @blur="keepFocus" border="none"
            :inputmode="modeVisible ? 'none' : 'none'" style="width: 100%;" placeholder="请扫描或输入客户码"></input>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>

        <u-form-item label="条码数量" labelWidth="100">
          <view class="w100x flex right"> {{  model.quantity || 0 }}/{{100 }} </view>
        </u-form-item>

      </u--form>
      <view class="ml5 fs16 mt20" style="color: #409eff"> 明细列表 </view>

      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">条码号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 80rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.lotName }}</view>

            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
              <view class="w80x" @click="deleteItem(ele, index)">
                <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>
      </view>

    <view class="btnContainer" @click="submitOfflineReturn">提交</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import _ from "lodash";
import useNls from "@/mixins/useNls";

export default {
  mixins: [useNls],
  components: {
    NoData,
  },
  data() {
    this.changebarcode = this.$debounce(this.changebarcode, 1000)
    return {
      isFocus: true,
      pageTitle: '',
      modeVisible: false,
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
        searchTitle: '查询结果'
      },
      dicts: {
      },
      list: [],
      model: {},
      columns: [],
      select: false,
      selectType: '',
    }
  },
  watch: {
    'model.lotName': {
      handler(val) {
        const cleaned = (val || '').replace(/[\s\r\n\t]/g, '').trim();
        this.model.lotName = cleaned
        this.changebarcode(cleaned)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.initModel()
  },

  methods: {
    deleteItem(item, index) {
      this.list.splice(index, 1)
      this.model.quantity = this.list.length;
    },
    initModel() {
      this.model = {
        lotName: '', 
        quantity: 0,
      }
      this.list = []
    },
    async changebarcode(value) {
      if(!value){
          return;
      }
      let param = {
        lotName: value,
      }
      // let res = await this.$service.BatteryCellOfflineReturn.getLotList(param)
	  const res = await this.$service.BatteryCellOfflineReturn.batteryCellOfflineReturnCheck(param);
	  const lotName = res.data
	  const exists = this.list.some(item => item.lotName === lotName);
	  if (exists) {
	    this.$Toast('该条码已添加，请勿重复扫描！');
	    return;
	  }
	  this.model.lotName = ''
	  this.list.push({
		lotName: lotName,
	  });
	  this.model.quantity = this.list.length;
      // if (res.success) {
      //   if(res.datas.length === 0){
      //   this.$Toast('条码不存在请检查！')
      //   return;
      //   }else{
      //     const lot = res.datas[0];
      //    // 判断是否已存在（防重复）
      //     const exists = this.list.some(item => item.lotName === lot.lotName);
      //     if (exists) {
      //       this.$Toast('该条码已添加，请勿重复扫描！');
      //       return;
      //     }
      //     this.model.lotName = ''
      //     this.list.push({
      //       lotName: lot.lotName,
      //     });
      //     this.model.quantity = this.list.length;
      //   }
      // }

    },
    async submitOfflineReturn() {
        const lotNames = this.list.map(item => item.lotName);

        if (lotNames.length === 0) {
          this.$Toast('请先扫码添加条码！');
          return;
        }

        const param = {
          lotNames: lotNames, 
        };

        try {
          const res = await this.$service.BatteryCellOfflineReturn.batteryCellOfflineReturn(param);
          if (res.success) {
            this.$Toast('退库成功');
            this.list = []; // 清空列表
            this.model.quantity = 0; // 清空数量
          } else {
            this.$Toast(res.msg || '退库失败');
          }
        } catch (error) {
          console.error('提交失败', error);
          this.$Toast(error.msg);
        }
      },
    keepFocus() {
      this.modeVisible = false;
      setTimeout(() => {
        this.isFocus = false; // 先置为 false
        this.$nextTick(() => {
          this.modeVisible = true;
          this.isFocus = true; // 再置为 true，确保光标恢复
        });
      }, 100);
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'lotName':
          this.model.lotName = 'C1Z001002'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          const raw = res.result || '';
          const cleaned = raw.replace(/\s+/g, '').trim(); // 清洗：去掉空格、换行、制表符
          this.$set(this.model, key, cleaned)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
